import type { AnyRealmObject } from "./Object";
import type { AnyCollection } from "./Collection";
import type { Counter } from "./Counter";
import type { AnyDictionary, Dictionary } from "./Dictionary";
import type { AnyList, List } from "./List";
import type { AnySet, RealmSet } from "./Set";
type ExtractPropertyNamesOfType<T, PropType> = {
    [K in keyof T]: T[K] extends PropType ? K : never;
}[keyof T];
type ExtractPropertyNamesOfTypeExcludingNullability<T, PropType> = {
    [K in keyof T]: Exclude<T[K], null | undefined> extends PropType ? K : never;
}[keyof T];
/**
 * Exchanges properties defined as {@link List} with an optional {@link Array}.
 */
type RealmListRemappedModelPart<T> = {
    [K in ExtractPropertyNamesOfType<T, AnyList>]?: T[K] extends List<infer GT> ? Array<GT | Unmanaged<GT>> : never;
};
/**
 * Exchanges properties defined as {@link Dictionary} with an optional key to mixed value object.
 */
type RealmDictionaryRemappedModelPart<T> = {
    [K in ExtractPropertyNamesOfType<T, AnyDictionary>]?: T[K] extends Dictionary<infer ValueType> ? {
        [key: string]: ValueType;
    } : never;
};
/**
 * Exchanges properties defined as {@link RealmSet} with an optional {@link Array}.
 */
type RealmSetRemappedModelPart<T> = {
    [K in ExtractPropertyNamesOfType<T, AnySet>]?: T[K] extends RealmSet<infer GT> ? Array<GT | Unmanaged<GT>> : never;
};
/**
 * Exchanges properties defined as a {@link Counter} with a `number`.
 */
type RealmCounterRemappedModelPart<T> = {
    [K in ExtractPropertyNamesOfTypeExcludingNullability<T, Counter>]?: Counter | number | Exclude<T[K], Counter>;
};
/** Omits all properties of a model which are not defined by the schema */
export type OmittedRealmTypes<T> = Omit<T, keyof AnyRealmObject | ExtractPropertyNamesOfType<T, Function> | ExtractPropertyNamesOfType<T, AnyCollection> | ExtractPropertyNamesOfType<T, AnyDictionary> | ExtractPropertyNamesOfTypeExcludingNullability<T, Counter>>;
/** Make all fields optional except those specified in K */
type OptionalExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;
/**
 * Omits all properties of a model which are not defined by the schema,
 * making all properties optional except those specified in RequiredProperties.
 */
type OmittedRealmTypesWithRequired<T, RequiredProperties extends keyof OmittedRealmTypes<T>> = OptionalExcept<OmittedRealmTypes<T>, RequiredProperties>;
/** Remaps realm types to "simpler" types (arrays and objects) */
type RemappedRealmTypes<T> = RealmListRemappedModelPart<T> & RealmDictionaryRemappedModelPart<T> & RealmSetRemappedModelPart<T> & RealmCounterRemappedModelPart<T>;
/**
 * Joins `T` stripped of all keys which value extends {@link Collection} and all inherited from {@link Realm.Object},
 * with only the keys which value extends {@link List}, remapped as {@link Array}. All properties are optional
 * except those specified in `RequiredProperties`.
 */
export type Unmanaged<T, RequiredProperties extends keyof OmittedRealmTypes<T> = never> = OmittedRealmTypesWithRequired<T, RequiredProperties> & RemappedRealmTypes<T>;
export {};
