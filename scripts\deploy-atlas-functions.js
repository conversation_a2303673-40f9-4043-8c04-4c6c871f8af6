#!/usr/bin/env node

/**
 * Deploy Atlas App Services Functions Programmatically
 * This script creates and deploys functions to Atlas App Services using code
 */

const fs = require('fs');
const path = require('path');

// Atlas App Services configuration
const ATLAS_CONFIG = {
  appId: 'trainfit-backend-sjcdpns',
  groupId: '685878c11ee071769a3fc035',
  // Note: You'll need to set these environment variables
  publicKey: process.env.ATLAS_PUBLIC_KEY,
  privateKey: process.env.ATLAS_PRIVATE_KEY
};

// Function definitions - same as your local Hono server logic
const FUNCTIONS = {
  'auth-register': {
    name: 'auth-register',
    private: false,
    run_as_system: true,
    source: fs.readFileSync(path.join(__dirname, '../atlas-functions/auth-register.js'), 'utf8')
  },
  'auth-login': {
    name: 'auth-login',
    private: false,
    run_as_system: true,
    source: fs.readFileSync(path.join(__dirname, '../atlas-functions/auth-login.js'), 'utf8')
  },
  'health-check': {
    name: 'health-check',
    private: false,
    run_as_system: true,
    source: fs.readFileSync(path.join(__dirname, '../atlas-functions/health-check.js'), 'utf8')
  }
};

// HTTPS Endpoints configuration
const ENDPOINTS = [
  {
    route: '/api/auth/register',
    http_method: 'POST',
    function_name: 'auth-register',
    respond_result: true
  },
  {
    route: '/api/auth/login',
    http_method: 'POST',
    function_name: 'auth-login',
    respond_result: true
  },
  {
    route: '/api/health',
    http_method: 'GET',
    function_name: 'health-check',
    respond_result: true
  }
];

/**
 * Atlas Admin API helper
 */
class AtlasAdminAPI {
  constructor(config) {
    this.config = config;
    this.baseUrl = 'https://services.cloud.mongodb.com/api/admin/v3.0';
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const auth = Buffer.from(`${this.config.publicKey}:${this.config.privateKey}`).toString('base64');
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Atlas API Error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  async createFunction(functionDef) {
    return this.makeRequest(`/groups/${this.config.groupId}/apps/${this.config.appId}/functions`, {
      method: 'POST',
      body: JSON.stringify(functionDef)
    });
  }

  async createEndpoint(endpointDef) {
    return this.makeRequest(`/groups/${this.config.groupId}/apps/${this.config.appId}/endpoints`, {
      method: 'POST',
      body: JSON.stringify(endpointDef)
    });
  }

  async deployApp() {
    return this.makeRequest(`/groups/${this.config.groupId}/apps/${this.config.appId}/deployment/deploy`, {
      method: 'POST'
    });
  }
}

/**
 * Main deployment function
 */
async function deployAtlasFunctions() {
  console.log('🚀 Deploying TrainFit Atlas App Services Functions');
  console.log('================================================');

  // Check for required environment variables
  if (!ATLAS_CONFIG.publicKey || !ATLAS_CONFIG.privateKey) {
    console.error('❌ Missing Atlas API credentials');
    console.log('Please set the following environment variables:');
    console.log('  ATLAS_PUBLIC_KEY=your-public-key');
    console.log('  ATLAS_PRIVATE_KEY=your-private-key');
    console.log('');
    console.log('Get your API keys from:');
    console.log('  https://cloud.mongodb.com/v2#/org/685878c01ee071769a3fc029/access/apiKeys');
    process.exit(1);
  }

  const api = new AtlasAdminAPI(ATLAS_CONFIG);

  try {
    // Deploy functions
    console.log('📝 Creating functions...');
    for (const [name, functionDef] of Object.entries(FUNCTIONS)) {
      console.log(`  • Creating function: ${name}`);
      await api.createFunction(functionDef);
    }

    // Deploy endpoints
    console.log('🔗 Creating HTTPS endpoints...');
    for (const endpoint of ENDPOINTS) {
      console.log(`  • Creating endpoint: ${endpoint.route}`);
      await api.createEndpoint(endpoint);
    }

    // Deploy the app
    console.log('🚀 Deploying app...');
    await api.deployApp();

    console.log('');
    console.log('✅ Deployment successful!');
    console.log('🔗 Your Atlas App Services URL:');
    console.log(`   https://data.mongodb-api.com/app/${ATLAS_CONFIG.appId}/endpoint`);
    console.log('');
    console.log('🎯 Next steps:');
    console.log('1. Stop your local backend server');
    console.log('2. Set USE_ATLAS_SDK = true in lib/api.ts');
    console.log('3. Test your mobile app - no more "network request failed" errors!');

  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    console.log('');
    console.log('💡 Alternative: Manual setup');
    console.log('1. Copy function code from atlas-functions/ directory');
    console.log('2. Paste into Atlas App Services dashboard');
    console.log('3. Create HTTPS endpoints manually');
    process.exit(1);
  }
}

// Run deployment if this script is executed directly
if (require.main === module) {
  deployAtlasFunctions();
}

module.exports = { deployAtlasFunctions, ATLAS_CONFIG, FUNCTIONS, ENDPOINTS };
