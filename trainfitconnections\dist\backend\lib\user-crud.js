"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserProfile = createUserProfile;
exports.getUserById = getUserById;
exports.updateUserById = updateUserById;
exports.deleteUserById = deleteUserById;
exports.getTrainers = getTrainers;
const mongodb_1 = __importDefault(require("./mongodb"));
const dbName = 'trainfitconnections';
// CREATE a user profile (not for registration - use auth.ts for that)
async function createUserProfile(userId, profileData) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const profile = {
        ...profileData,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('user_profiles').insertOne(profile);
    return result.insertedId;
}
// READ a user by id (only returns user's own data)
async function getUserById(id, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own data
    if (id !== requestingUserId) {
        throw new Error('Access denied: You can only access your own data');
    }
    return db.collection('users').findOne({ id }, { projection: { passwordHash: 0 } });
}
// UPDATE a user by id (only allows updating own data)
async function updateUserById(id, update, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only update their own data
    if (id !== requestingUserId) {
        throw new Error('Access denied: You can only update your own data');
    }
    // Remove sensitive fields that shouldn't be updated via this method
    const { passwordHash, id: userId, email, ...safeUpdate } = update;
    const updateData = {
        ...safeUpdate,
        updatedAt: new Date(),
    };
    return db.collection('users').updateOne({ id }, { $set: updateData });
}
// DELETE a user by id (only allows deleting own account)
async function deleteUserById(id, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only delete their own account
    if (id !== requestingUserId) {
        throw new Error('Access denied: You can only delete your own account');
    }
    return db.collection('users').deleteOne({ id });
}
// Get trainers list (public data only)
async function getTrainers() {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    return db.collection('users').find({ role: 'trainer' }, { projection: { id: 1, name: 1, email: 1, role: 1, createdAt: 1 } }).toArray();
}
//# sourceMappingURL=user-crud.js.map