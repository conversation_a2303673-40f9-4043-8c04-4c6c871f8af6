import type { binding } from "./binding";
import type { CanonicalObjectSchema, DefaultObject, RealmObjectConstructor } from "./schema";
import type { PropertyMap } from "./PropertyMap";
import type { RealmObject } from "./Object";
type ObjectWrapper = (obj: binding.Obj) => (RealmObject & DefaultObject) | null;
/** @internal */
export type ClassHelpers = {
    constructor: RealmObjectConstructor;
    objectSchema: binding.ObjectSchema;
    properties: PropertyMap;
    wrapObject: ObjectWrapper;
    canonicalObjectSchema: CanonicalObjectSchema;
};
/** @internal */
export declare function setClassHelpers(constructor: RealmObjectConstructor, value: ClassHelpers): void;
/**
 * Get internal helpers.
 * NOTE: This is a free function instead of a member of RealmObject to limit conflicts with user defined properties.
 * @param arg The object or constructor to get a helpers for.
 * @returns Helpers injected onto the class by the `ClassMap`.
 * @internal
 */
export declare function getClassHelpers(arg: typeof RealmObject): ClassHelpers;
export {};
