export declare const flags: {
    /**
     * When enabled, objects can be created by providing an array of values (in the order that they were declared in the object schema) in addition to of an object of property values.
     */
    ALLOW_VALUES_ARRAYS: boolean;
    /**
     * When enabled, accessing the `Realm` without first importing it from the Realm package, will throw.
     * Helps finding places where the app is depending on the now deprecated way of using the package.
     */
    THROW_ON_GLOBAL_REALM: boolean;
    /**
     * Enables calling internal, test-only functions like `Realm.clearTestState`.
     * This is disabled by default, mainly because the data-structures needed to support this, introduce minor memory leaks if clearTestState() is not called regularly and are not intended for production use.
     */
    ALLOW_CLEAR_TEST_STATE: boolean;
};
//# sourceMappingURL=flags.d.ts.map