{"name": "trainfit-backend", "version": "1.0.0", "description": "TrainFit Backend API", "main": "server.ts", "scripts": {"dev": "tsx watch server.ts", "build": "tsc", "start": "node dist/server.js", "vercel-build": "tsc"}, "dependencies": {"@hono/node-server": "^1.12.2", "hono": "^4.6.3", "mongodb": "^6.9.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.23.8", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}