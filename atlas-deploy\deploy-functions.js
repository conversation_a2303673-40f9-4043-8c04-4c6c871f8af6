#!/usr/bin/env node

/**
 * Atlas App Services Deployment Script
 * This script automatically creates and deploys functions to your Atlas App Services app
 */

const fs = require('fs');
const path = require('path');

// Your Atlas App Services configuration
const ATLAS_CONFIG = {
  appId: 'trainfit-backend-sjcdpns',
  baseUrl: 'https://services.cloud.mongodb.com/api/admin/v3.0',
  // You'll need to get these from Atlas
  publicKey: process.env.ATLAS_PUBLIC_KEY,
  privateKey: process.env.ATLAS_PRIVATE_KEY,
  groupId: '685878c11ee071769a3fc035'
};

// Function definitions
const FUNCTIONS = {
  'auth-register': {
    name: 'auth-register',
    private: false,
    run_as_system: true,
    source: `
exports = function(request, response) {
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }
  
  try {
    const body = JSON.parse(request.body.text());
    const { email, password, name, role } = body;
    
    if (!email || !password || !name || !role) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing required fields" }));
      return;
    }
    
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");
    
    return users.findOne({ email }).then(existingUser => {
      if (existingUser) {
        response.setStatusCode(400);
        response.setBody(JSON.stringify({ error: "User already exists" }));
        return;
      }
      
      const userId = new BSON.ObjectId();
      const user = {
        _id: userId,
        id: userId.toString(),
        email,
        name,
        role,
        password,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      return users.insertOne(user).then(() => {
        const token = \`trainfit_\${userId.toString()}_\${Date.now()}\`;
        
        response.setStatusCode(201);
        response.setBody(JSON.stringify({
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          },
          token
        }));
      });
    }).catch(error => {
      response.setStatusCode(500);
      response.setBody(JSON.stringify({ error: error.message }));
    });
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: "Invalid request body" }));
  }
};`
  },
  
  'auth-login': {
    name: 'auth-login',
    private: false,
    run_as_system: true,
    source: `
exports = function(request, response) {
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }
  
  try {
    const body = JSON.parse(request.body.text());
    const { email, password } = body;
    
    if (!email || !password) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing email or password" }));
      return;
    }
    
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");
    
    return users.findOne({ email }).then(user => {
      if (!user) {
        response.setStatusCode(401);
        response.setBody(JSON.stringify({ error: "Invalid credentials" }));
        return;
      }
      
      if (user.password !== password) {
        response.setStatusCode(401);
        response.setBody(JSON.stringify({ error: "Invalid credentials" }));
        return;
      }
      
      const token = \`trainfit_\${user._id.toString()}_\${Date.now()}\`;
      
      response.setStatusCode(200);
      response.setBody(JSON.stringify({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        token
      }));
    }).catch(error => {
      response.setStatusCode(500);
      response.setBody(JSON.stringify({ error: error.message }));
    });
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: "Invalid request body" }));
  }
};`
  },
  
  'health-check': {
    name: 'health-check',
    private: false,
    run_as_system: true,
    source: `
exports = function(request, response) {
  response.setStatusCode(200);
  response.setBody(JSON.stringify({
    status: "ok",
    timestamp: new Date().toISOString(),
    service: "TrainFit Backend",
    version: "1.0.0"
  }));
};`
  }
};

// HTTPS Endpoints configuration
const ENDPOINTS = [
  {
    route: '/api/auth/register',
    http_method: 'POST',
    function_name: 'auth-register',
    respond_result: true,
    fetch_custom_user_data: false,
    create_user_on_auth: false,
    disabled: false
  },
  {
    route: '/api/auth/login',
    http_method: 'POST',
    function_name: 'auth-login',
    respond_result: true,
    fetch_custom_user_data: false,
    create_user_on_auth: false,
    disabled: false
  },
  {
    route: '/api/health',
    http_method: 'GET',
    function_name: 'health-check',
    respond_result: true,
    fetch_custom_user_data: false,
    create_user_on_auth: false,
    disabled: false
  }
];

console.log('🚀 Atlas App Services Deployment Script');
console.log('📋 Functions to deploy:', Object.keys(FUNCTIONS));
console.log('🔗 Endpoints to create:', ENDPOINTS.length);
console.log('');
console.log('⚠️  To use this script, you need to:');
console.log('1. Get your Atlas API keys from: https://cloud.mongodb.com/v2#/org/685878c01ee071769a3fc029/access/apiKeys');
console.log('2. Set environment variables:');
console.log('   export ATLAS_PUBLIC_KEY="your-public-key"');
console.log('   export ATLAS_PRIVATE_KEY="your-private-key"');
console.log('3. Run: node deploy-functions.js');
console.log('');
console.log('📖 For manual deployment, copy the function code from this file to Atlas dashboard.');
