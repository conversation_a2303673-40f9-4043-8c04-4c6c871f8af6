"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.OBJECT_HELPERS = exports.OBJECT_REALM = exports.OBJECT_INTERNAL = void 0;
exports.OBJECT_INTERNAL = Symbol("Object#internal");
exports.OBJECT_REALM = Symbol("Object#realm");
exports.OBJECT_HELPERS = Symbol("Object#helpers");
//# sourceMappingURL=symbols.js.map