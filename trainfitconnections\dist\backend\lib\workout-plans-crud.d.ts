import { ObjectId } from 'mongodb';
export interface Exercise {
    id: string;
    name: string;
    description?: string;
    sets: number;
    reps: number | string;
    weight?: number;
    duration?: string;
    restTime?: string;
    notes?: string;
    videoUrl?: string;
    imageUrl?: string;
    targetMuscles?: string[];
    equipment?: string[];
}
export interface WorkoutPlan {
    _id?: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    title: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    startDate: string;
    endDate: string;
    exercises: Exercise[];
    notes?: string;
    status: 'active' | 'completed' | 'archived';
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedDuration?: number;
    category?: string;
    tags?: string[];
    isTemplate?: boolean;
}
export declare function createWorkoutPlan(planData: Omit<WorkoutPlan, '_id' | 'id' | 'createdAt' | 'updatedAt'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    title: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    startDate: string;
    endDate: string;
    exercises: Exercise[];
    notes?: string;
    status: "active" | "completed" | "archived";
    difficulty: "beginner" | "intermediate" | "advanced";
    estimatedDuration?: number;
    category?: string;
    tags?: string[];
    isTemplate?: boolean;
}>;
export declare function getWorkoutPlansForUser(userId: string, requestingUserId: string, filters?: {
    status?: string;
    difficulty?: string;
    category?: string;
    isTemplate?: boolean;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getWorkoutPlanById(planId: string, requestingUserId: string): Promise<WorkoutPlan>;
export declare function updateWorkoutPlan(planId: string, updates: Partial<WorkoutPlan>, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteWorkoutPlan(planId: string, requestingUserId: string): Promise<import("mongodb").DeleteResult>;
export declare function getTrainerWorkoutPlans(trainerId: string, requestingUserId: string, filters?: {
    status?: string;
    isTemplate?: boolean;
    clientId?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getClientWorkoutPlans(clientId: string, requestingUserId: string, filters?: {
    status?: string;
    difficulty?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function assignWorkoutPlanToClient(planId: string, clientId: string, requestingUserId: string, customizations?: {
    startDate?: string;
    endDate?: string;
    notes?: string;
}): Promise<{
    _id: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    title: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    startDate: string;
    endDate: string;
    exercises: Exercise[];
    notes?: string;
    status: "active" | "completed" | "archived";
    difficulty: "beginner" | "intermediate" | "advanced";
    estimatedDuration?: number;
    category?: string;
    tags?: string[];
    isTemplate?: boolean;
}>;
//# sourceMappingURL=workout-plans-crud.d.ts.map