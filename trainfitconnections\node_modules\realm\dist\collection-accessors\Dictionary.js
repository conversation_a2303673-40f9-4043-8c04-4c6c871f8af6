"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.isPOJO = exports.isJsOrRealmDictionary = exports.insertIntoDictionaryOfMixed = exports.createDictionaryAccessor = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const indirect_1 = require("../indirect");
const List_1 = require("./List");
/** @internal */
function createDictionaryAccessor(options) {
    return options.itemType === 9 /* binding.PropertyType.Mixed */
        ? createDictionaryAccessorForMixed(options)
        : createDictionaryAccessorForKnownType(options);
}
exports.createDictionaryAccessor = createDictionaryAccessor;
function createDictionaryAccessorForMixed({ realm, typeHelpers, }) {
    const { toBinding, fromBinding } = typeHelpers;
    return {
        get(dictionary, key) {
            const value = dictionary.tryGetAny(key);
            switch (value) {
                case binding_1.binding.ListSentinel: {
                    const accessor = (0, List_1.createListAccessor)({ realm, itemType: 9 /* binding.PropertyType.Mixed */, typeHelpers });
                    return new indirect_1.indirect.List(realm, dictionary.getList(key), accessor, typeHelpers);
                }
                case binding_1.binding.DictionarySentinel: {
                    const accessor = createDictionaryAccessor({ realm, itemType: 9 /* binding.PropertyType.Mixed */, typeHelpers });
                    return new indirect_1.indirect.Dictionary(realm, dictionary.getDictionary(key), accessor, typeHelpers);
                }
                default:
                    return fromBinding(value);
            }
        },
        set(dictionary, key, value) {
            assert_1.assert.inTransaction(realm);
            if ((0, List_1.isJsOrRealmList)(value)) {
                dictionary.insertCollection(key, 19 /* binding.CollectionType.List */);
                (0, List_1.insertIntoListOfMixed)(value, dictionary.getList(key), toBinding);
            }
            else if (isJsOrRealmDictionary(value)) {
                dictionary.insertCollection(key, 21 /* binding.CollectionType.Dictionary */);
                insertIntoDictionaryOfMixed(value, dictionary.getDictionary(key), toBinding);
            }
            else {
                dictionary.insertAny(key, toBinding(value));
            }
        },
    };
}
function createDictionaryAccessorForKnownType({ realm, typeHelpers, isEmbedded, }) {
    const { fromBinding, toBinding } = typeHelpers;
    return {
        get(dictionary, key) {
            return fromBinding(dictionary.tryGetAny(key));
        },
        set(dictionary, key, value) {
            assert_1.assert.inTransaction(realm);
            if (isEmbedded) {
                toBinding(value, { createObj: () => [dictionary.insertEmbedded(key), true] });
            }
            else {
                dictionary.insertAny(key, toBinding(value));
            }
        },
    };
}
/** @internal */
function insertIntoDictionaryOfMixed(dictionary, internal, toBinding) {
    // TODO: Solve the "removeAll()" case for self-assignment (https://github.com/realm/realm-core/issues/7422).
    internal.removeAll();
    for (const key in dictionary) {
        const value = dictionary[key];
        if ((0, List_1.isJsOrRealmList)(value)) {
            internal.insertCollection(key, 19 /* binding.CollectionType.List */);
            (0, List_1.insertIntoListOfMixed)(value, internal.getList(key), toBinding);
        }
        else if (isJsOrRealmDictionary(value)) {
            internal.insertCollection(key, 21 /* binding.CollectionType.Dictionary */);
            insertIntoDictionaryOfMixed(value, internal.getDictionary(key), toBinding);
        }
        else {
            internal.insertAny(key, toBinding(value));
        }
    }
}
exports.insertIntoDictionaryOfMixed = insertIntoDictionaryOfMixed;
/** @internal */
function isJsOrRealmDictionary(value) {
    return isPOJO(value) || value instanceof indirect_1.indirect.Dictionary;
}
exports.isJsOrRealmDictionary = isJsOrRealmDictionary;
/** @internal */
function isPOJO(value) {
    return (typeof value === "object" &&
        value !== null &&
        // Lastly check for the absence of a prototype as POJOs
        // can still be created using `Object.create(null)`.
        (value.constructor === Object || !Object.getPrototypeOf(value)));
}
exports.isPOJO = isPOJO;
//# sourceMappingURL=Dictionary.js.map