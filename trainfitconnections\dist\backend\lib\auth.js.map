{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../backend/lib/auth.ts"], "names": [], "mappings": ";;;;;AA6BA,oCAEC;AAGD,wCAEC;AAGD,sCAQC;AAGD,kCAOC;AAGD,oCAuCC;AAGD,8BA0BC;AAGD,kCAWC;AA9ID,gEAA+B;AAC/B,wDAA8B;AAC9B,wDAAsC;AACtC,qCAAmC;AAEnC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,gDAAgD,CAAC;AAC9F,MAAM,cAAc,GAAG,IAAI,CAAC;AAC5B,MAAM,WAAW,GAAG,EAAE,CAAC;AAqBvB,gBAAgB;AACT,KAAK,UAAU,YAAY,CAAC,QAAgB;IACjD,OAAO,kBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC5C,CAAC;AAED,kBAAkB;AACX,KAAK,UAAU,cAAc,CAAC,QAAgB,EAAE,IAAY;IACjE,OAAO,kBAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC;AAED,qBAAqB;AACrB,SAAgB,aAAa,CAAC,IAAU;IACtC,MAAM,OAAO,GAAqB;QAChC,MAAM,EAAE,IAAI,CAAC,EAAE;QACf,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,IAAI,EAAE,IAAI,CAAC,IAAI;KAChB,CAAC;IAEF,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC;AACtE,CAAC;AAED,mBAAmB;AACnB,SAAgB,WAAW,CAAC,KAAa;IACvC,IAAI,CAAC;QACH,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAqB,CAAC;IAC3D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,oBAAoB;AACb,KAAK,UAAU,YAAY,CAChC,KAAa,EACb,QAAgB,EAChB,IAAY,EACZ,IAA0B;IAE1B,MAAM,MAAM,GAAG,MAAM,iBAAa,CAAC;IACnC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAE5C,+BAA+B;IAC/B,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAC1F,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACzD,CAAC;IAED,gBAAgB;IAChB,MAAM,YAAY,GAAG,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC;IAElD,cAAc;IACd,MAAM,MAAM,GAAG,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;IACzC,MAAM,IAAI,GAAS;QACjB,EAAE,EAAE,MAAM;QACV,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE;QAC1B,IAAI;QACJ,IAAI;QACJ,YAAY;QACZ,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,SAAS,EAAE,IAAI,IAAI,EAAE;KACtB,CAAC;IAEF,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAE7C,iBAAiB;IACjB,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IAElC,0CAA0C;IAC1C,MAAM,EAAE,YAAY,EAAE,CAAC,EAAE,GAAG,mBAAmB,EAAE,GAAG,IAAI,CAAC;IAEzD,OAAO,EAAE,IAAI,EAAE,mBAA2B,EAAE,KAAK,EAAE,CAAC;AACtD,CAAC;AAED,aAAa;AACN,KAAK,UAAU,SAAS,CAC7B,KAAa,EACb,QAAgB;IAEhB,MAAM,MAAM,GAAG,MAAM,iBAAa,CAAC;IACnC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAE5C,YAAY;IACZ,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,CAAgB,CAAC;IACjG,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC/C,CAAC;IAED,kBAAkB;IAClB,MAAM,eAAe,GAAG,MAAM,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1E,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC/C,CAAC;IAED,iBAAiB;IACjB,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IAElC,0CAA0C;IAC1C,MAAM,EAAE,YAAY,EAAE,CAAC,EAAE,GAAG,mBAAmB,EAAE,GAAG,IAAI,CAAC;IAEzD,OAAO,EAAE,IAAI,EAAE,mBAA2B,EAAE,KAAK,EAAE,CAAC;AACtD,CAAC;AAED,iBAAiB;AACV,KAAK,UAAU,WAAW,CAAC,MAAc;IAC9C,MAAM,MAAM,GAAG,MAAM,iBAAa,CAAC;IACnC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAE5C,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAgB,CAAC;IACjF,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,EAAE,YAAY,EAAE,CAAC,EAAE,GAAG,mBAAmB,EAAE,GAAG,IAAI,CAAC;QACzD,OAAO,mBAA2B,CAAC;IACrC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}