import * as bson from "bson";
/**
 * Re-export a subset of the "bson" package, enabling access to the BSON types without requiring an explicit dependency on the "bson" package.
 * @see {@link https://www.npmjs.com/package/bson#documentation|the BSON documentation} for more information.
 */
export declare namespace BSON {
    const ObjectId: typeof bson.ObjectID;
    type ObjectId = bson.ObjectId;
    const ObjectID: typeof bson.ObjectID;
    type ObjectID = bson.ObjectID;
    const Decimal128: typeof bson.Decimal128;
    type Decimal128 = bson.Decimal128;
    const UUID: typeof bson.UUID;
    type UUID = bson.UUID;
    const Binary: typeof bson.Binary;
    type Binary = bson.Binary;
    const EJSON: typeof bson.EJSON;
    type EJSON = typeof bson.EJSON;
}
//# sourceMappingURL=bson.d.ts.map