/** @internal */
export declare const TYPED_ARRAY_CONSTRUCTORS: Set<Int8ArrayConstructor | DataViewConstructor | Uint8ArrayConstructor | Uint8ClampedArrayConstructor | Int16ArrayConstructor | Uint16ArrayConstructor | Int32ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor | BigInt64ArrayConstructor | BigUint64ArrayConstructor>;
/** @internal */
export declare function toArrayBuffer(value: unknown, stringToBase64?: boolean): A<PERSON><PERSON><PERSON>uffer;
