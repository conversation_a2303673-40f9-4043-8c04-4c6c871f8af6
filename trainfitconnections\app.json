{"expo": {"name": "TrainFit", "slug": "trainfitconnections", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.trainfit-connections", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "This app uses the camera to let you take profile and progress photos, and to scan QR codes for workouts or classes.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library so you can upload progress pictures and personalize your profile.", "NSLocationWhenInUseUsageDescription": "This app uses your location to find nearby gyms, trainers, and fitness facilities to enhance your workout experience.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses your location to find nearby gyms, trainers, and fitness facilities to enhance your workout experience.", "NSMicrophoneUsageDescription": "This app uses the microphone to record workout videos and voice notes for your training sessions.", "NSContactsUsageDescription": "This app accesses your contacts to help you find friends who are also using TrainFit and invite them to join your fitness journey.", "NSCalendarsUsageDescription": "This app accesses your calendar to schedule workout sessions and training appointments with your trainer.", "NSRemindersUsageDescription": "This app accesses your reminders to help you stay on track with your fitness goals and workout schedules.", "NSHealthShareUsageDescription": "This app integrates with Apple Health to track your fitness progress, workouts, and health metrics to provide personalized training recommendations.", "NSHealthUpdateUsageDescription": "This app updates Apple Health with your workout data, progress photos, and fitness achievements to keep your health records up to date.", "NSMotionUsageDescription": "This app uses motion data to track your workouts, count steps, and monitor your physical activity during training sessions.", "NSBluetoothAlwaysUsageDescription": "This app uses Bluetooth to connect with fitness devices, heart rate monitors, and other workout equipment to enhance your training experience.", "NSBluetoothPeripheralUsageDescription": "This app uses Bluetooth to connect with fitness devices, heart rate monitors, and other workout equipment to enhance your training experience.", "NSFaceIDUsageDescription": "This app uses Face ID to securely authenticate your identity and protect your personal fitness data and payment information.", "NSUserTrackingUsageDescription": "This app uses tracking to provide personalized workout recommendations and improve your fitness experience while respecting your privacy."}, "config": {"googleMapsApiKey": "AIzaSyArz11kMLQfBlGrtLXmqYPZB_lb_zFVuxM"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.trainfit_connections", "config": {"googleMaps": {"apiKey": "AIzaSyArz11kMLQfBlGrtLXmqYPZB_lb_zFVuxM"}}}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.app/"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": "https://rork.app/"}, "eas": {"projectId": "11cd59da-7e1d-451c-a74b-66968ff0d617"}}}}