{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "build:backend": "tsc --project backend/tsconfig.json", "check:backend": "tsc --project backend/tsconfig.json --noEmit", "check:frontend": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hono/node-server": "^1.14.4", "@hono/trpc-server": "^0.3.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.75.5", "@trpc/client": "^11.1.2", "@trpc/react-query": "^11.1.2", "@trpc/server": "^11.1.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "expo": "^53.0.4", "expo-av": "~15.1.4", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-camera": "~16.1.6", "expo-constants": "~17.1.4", "expo-dev-client": "~5.1.8", "expo-document-picker": "~13.1.5", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-router": "~5.0.3", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "hono": "^4.7.8", "jsonwebtoken": "^9.0.2", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "^13.13.5", "superjson": "^2.2.2", "zod": "^3.24.4", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true, "overrides": {"lucide-react-native": {"react": "19.0.0"}}}