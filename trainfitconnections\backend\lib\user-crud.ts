import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

// CREATE a user profile (not for registration - use auth.ts for that)
export async function createUserProfile(userId: string, profileData: any) {
  const client = await clientPromise;
  const db = client.db(dbName);

  const profile = {
    ...profileData,
    userId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const result = await db.collection('user_profiles').insertOne(profile);
  return result.insertedId;
}

// READ a user by id (only returns user's own data)
export async function getUserById(id: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);

  // Users can only access their own data
  if (id !== requestingUserId) {
    throw new Error('Access denied: You can only access your own data');
  }

  return db.collection('users').findOne({ id }, { projection: { passwordHash: 0 } });
}

// UPDATE a user by id (only allows updating own data)
export async function updateUserById(id: string, update: any, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);

  // Users can only update their own data
  if (id !== requestingUserId) {
    throw new Error('Access denied: You can only update your own data');
  }

  // Remove sensitive fields that shouldn't be updated via this method
  const { passwordHash, id: userId, email, ...safeUpdate } = update;

  const updateData = {
    ...safeUpdate,
    updatedAt: new Date(),
  };

  return db.collection('users').updateOne({ id }, { $set: updateData });
}

// DELETE a user by id (only allows deleting own account)
export async function deleteUserById(id: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);

  // Users can only delete their own account
  if (id !== requestingUserId) {
    throw new Error('Access denied: You can only delete your own account');
  }

  return db.collection('users').deleteOne({ id });
}

// Get trainers list (public data only)
export async function getTrainers() {
  const client = await clientPromise;
  const db = client.db(dbName);

  return db.collection('users').find(
    { role: 'trainer' },
    { projection: { id: 1, name: 1, email: 1, role: 1, createdAt: 1 } }
  ).toArray();
}
