import type { RealmObject } from "./Object";
export type ObjectChangeSet<T> = {
    /**
     * Is `true` if the object has been deleted.
     */
    deleted: boolean;
    /**
     * An array of properties that have changed their value.
     */
    changedProperties: (keyof T)[];
};
export type ObjectChangeCallback<T> = (
/**
 * The object that changed.
 */
object: RealmObject<T> & T, 
/**
 * A dictionary with information about the changes.
 */
changes: ObjectChangeSet<T>) => void;
//# sourceMappingURL=ObjectListeners.d.ts.map