{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../backend/routes/auth.ts"], "names": [], "mappings": ";;;AAAA,+BAA4B;AAC5B,sCAAsD;AACtD,6CAAoE;AAEpE,MAAM,IAAI,GAAG,IAAI,WAAI,EAAE,CAAC;AAiFf,oBAAI;AA/Eb,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;IACjC,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAE3D,aAAa;QACb,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC1C,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,GAAG,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,GAAG,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6CAA6C,EAAE,EAAE,GAAG,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,GAAG,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAA,mBAAY,EAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAExE,OAAO,CAAC,CAAC,IAAI,CAAC;YACZ,OAAO,EAAE,8BAA8B;YACvC,IAAI;YACJ,KAAK;SACN,EAAE,GAAG,CAAC,CAAC;IAEV,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAC,IAAI,CAAC;YACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB;SACtE,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,iBAAiB;AACjB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;IAC9B,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAE/C,aAAa;QACb,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,GAAG,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAA,gBAAS,EAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAEzD,OAAO,CAAC,CAAC,IAAI,CAAC;YACZ,OAAO,EAAE,kBAAkB;YAC3B,IAAI;YACJ,KAAK;SACN,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,CAAC,CAAC,IAAI,CAAC;YACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc;SAC/D,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,4BAA4B;AAC5B,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,qBAAc,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;IAChD,MAAM,IAAI,GAAG,IAAA,qBAAc,EAAC,CAAC,CAAC,CAAC;IAE/B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,CAAC,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE,gBAAgB;QACzB,IAAI;KACL,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}