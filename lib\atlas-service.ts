/**
 * Atlas App Services SDK Integration
 * This replaces the local backend server with direct Atlas App Services calls
 */

import Realm from 'realm';

// Atlas App Services configuration
const ATLAS_APP_ID = 'trainfit-backend-sjcdpns';

class AtlasService {
  private app: Realm.App;
  private user: Realm.User | null = null;

  constructor() {
    this.app = new Realm.App({ id: ATLAS_APP_ID });
  }

  /**
   * Initialize the Atlas service
   */
  async initialize() {
    try {
      // Log in anonymously to access system functions
      this.user = await this.app.logIn(Realm.Credentials.anonymous());
      console.log('✅ Atlas App Services connected');
      return true;
    } catch (error) {
      console.error('❌ Atlas connection failed:', error);
      return false;
    }
  }

  /**
   * Register a new user
   */
  async registerUser(userData: {
    email: string;
    password: string;
    name: string;
    role: 'client' | 'trainer';
  }) {
    try {
      if (!this.user) {
        await this.initialize();
      }

      // Call the Atlas function directly
      const result = await this.user?.functions.callFunction('auth-register', userData);
      
      if (result?.error) {
        throw new Error(result.error);
      }

      return {
        success: true,
        user: result.user,
        token: result.token
      };
    } catch (error: any) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'Registration failed'
      };
    }
  }

  /**
   * Login user
   */
  async loginUser(credentials: {
    email: string;
    password: string;
  }) {
    try {
      if (!this.user) {
        await this.initialize();
      }

      // Call the Atlas function directly
      const result = await this.user?.functions.callFunction('auth-login', credentials);
      
      if (result?.error) {
        throw new Error(result.error);
      }

      return {
        success: true,
        user: result.user,
        token: result.token
      };
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      if (!this.user) {
        await this.initialize();
      }

      const result = await this.user?.functions.callFunction('health-check');
      return result;
    } catch (error) {
      console.error('Health check failed:', error);
      return { status: 'error', error: error };
    }
  }

  /**
   * Get MongoDB database access (for direct operations)
   */
  async getDatabase() {
    try {
      if (!this.user) {
        await this.initialize();
      }

      return this.user?.mongoClient('mongodb-atlas').db('trainfit');
    } catch (error) {
      console.error('Database access failed:', error);
      return null;
    }
  }

  /**
   * Direct database operations (bypassing functions)
   */
  async createUser(userData: any) {
    try {
      const db = await this.getDatabase();
      const users = db?.collection('users');
      
      const result = await users?.insertOne({
        ...userData,
        _id: new Realm.BSON.ObjectId(),
        createdAt: new Date(),
        updatedAt: new Date()
      });

      return result;
    } catch (error) {
      console.error('Direct user creation failed:', error);
      throw error;
    }
  }

  async findUser(query: any) {
    try {
      const db = await this.getDatabase();
      const users = db?.collection('users');
      
      const result = await users?.findOne(query);
      return result;
    } catch (error) {
      console.error('User lookup failed:', error);
      throw error;
    }
  }

  /**
   * Logout
   */
  async logout() {
    try {
      if (this.user) {
        await this.user.logOut();
        this.user = null;
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
}

// Export singleton instance
export const atlasService = new AtlasService();
export default atlasService;
