/**
 * Atlas App Services SDK Integration
 * This replaces the local backend server with direct Atlas App Services calls
 */

import Realm from 'realm';

// Atlas App Services configuration
const ATLAS_APP_ID = 'trainfit-backend-sjcdpns';

class AtlasService {
  private app: Realm.App;
  private user: Realm.User | null = null;

  constructor() {
    this.app = new Realm.App({ id: ATLAS_APP_ID });
  }

  /**
   * Initialize the Atlas service (no auto-login for security)
   */
  async initialize() {
    try {
      // Just initialize the app, don't auto-login
      console.log('✅ Atlas App Services initialized');
      return true;
    } catch (error) {
      console.error('❌ Atlas connection failed:', error);
      return false;
    }
  }

  /**
   * Register a new user with secure Atlas authentication
   */
  async registerUser(userData: {
    email: string;
    password: string;
    name: string;
    role: 'client' | 'trainer';
  }) {
    try {
      // Register user with Atlas App Services Email/Password authentication
      await this.app.emailPasswordAuth.registerUser({
        email: userData.email,
        password: userData.password
      });

      // Log in the newly registered user
      const credentials = Realm.Credentials.emailPassword(userData.email, userData.password);
      this.user = await this.app.logIn(credentials);

      // Store additional user data in MongoDB
      const mongodb = this.user.mongoClient('mongodb-atlas');
      const users = mongodb.db('trainfit').collection('users');

      const userDoc = {
        _id: new Realm.BSON.ObjectId(),
        userId: this.user.id, // Atlas user ID for security
        email: userData.email,
        name: userData.name,
        role: userData.role,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await users.insertOne(userDoc);

      return {
        success: true,
        user: {
          id: userDoc._id.toString(),
          email: userDoc.email,
          name: userDoc.name,
          role: userDoc.role
        },
        token: this.user.accessToken || 'atlas-authenticated'
      };
    } catch (error: any) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'Registration failed'
      };
    }
  }

  /**
   * Login user with secure Atlas authentication
   */
  async loginUser(credentials: {
    email: string;
    password: string;
  }) {
    try {
      // Log in with Atlas App Services Email/Password authentication
      const atlasCredentials = Realm.Credentials.emailPassword(credentials.email, credentials.password);
      this.user = await this.app.logIn(atlasCredentials);

      // Get user data from MongoDB
      const mongodb = this.user.mongoClient('mongodb-atlas');
      const users = mongodb.db('trainfit').collection('users');

      const userDoc = await users.findOne({
        userId: this.user.id // Match by Atlas user ID for security
      });

      if (!userDoc) {
        throw new Error('User profile not found');
      }

      return {
        success: true,
        user: {
          id: userDoc._id.toString(),
          email: userDoc.email,
          name: userDoc.name,
          role: userDoc.role
        },
        token: this.user.accessToken || 'atlas-authenticated'
      };
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.message || 'Invalid credentials'
      };
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      if (!this.user) {
        await this.initialize();
      }

      const result = await this.user?.functions.callFunction('health-check');
      return result;
    } catch (error) {
      console.error('Health check failed:', error);
      return { status: 'error', error: error };
    }
  }

  /**
   * Get MongoDB database access (for direct operations)
   */
  async getDatabase() {
    try {
      if (!this.user) {
        await this.initialize();
      }

      return this.user?.mongoClient('mongodb-atlas').db('trainfit');
    } catch (error) {
      console.error('Database access failed:', error);
      return null;
    }
  }

  /**
   * Direct database operations (bypassing functions)
   */
  async createUser(userData: any) {
    try {
      const db = await this.getDatabase();
      const users = db?.collection('users');
      
      const result = await users?.insertOne({
        ...userData,
        _id: new Realm.BSON.ObjectId(),
        createdAt: new Date(),
        updatedAt: new Date()
      });

      return result;
    } catch (error) {
      console.error('Direct user creation failed:', error);
      throw error;
    }
  }

  async findUser(query: any) {
    try {
      const db = await this.getDatabase();
      const users = db?.collection('users');
      
      const result = await users?.findOne(query);
      return result;
    } catch (error) {
      console.error('User lookup failed:', error);
      throw error;
    }
  }

  /**
   * Logout
   */
  async logout() {
    try {
      if (this.user) {
        await this.user.logOut();
        this.user = null;
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
}

// Export singleton instance
export const atlasService = new AtlasService();
export default atlasService;
