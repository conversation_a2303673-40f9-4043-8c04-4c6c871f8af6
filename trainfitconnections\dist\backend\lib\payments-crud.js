"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPayment = createPayment;
exports.getPaymentsForUser = getPaymentsForUser;
exports.getPaymentById = getPaymentById;
exports.updatePayment = updatePayment;
exports.getTrainerRevenue = getTrainerRevenue;
exports.calculateRevenue = calculateRevenue;
exports.processRefund = processRefund;
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const dbName = 'trainfitconnections';
// CREATE a payment
async function createPayment(paymentData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Verify user can create this payment (must be client or trainer involved)
    if (paymentData.trainerId !== requestingUserId && paymentData.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only create payments you are involved in');
    }
    const paymentId = new mongodb_2.ObjectId().toString();
    const payment = {
        ...paymentData,
        id: paymentId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('payments').insertOne(payment);
    return { ...payment, _id: result.insertedId };
}
// READ payments for a user
async function getPaymentsForUser(userId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own payments
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own payments');
    }
    const query = {
        $or: [
            { trainerId: userId },
            { clientId: userId }
        ]
    };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.dateFrom || filters?.dateTo) {
        query.createdAt = {};
        if (filters.dateFrom) {
            query.createdAt.$gte = new Date(filters.dateFrom);
        }
        if (filters.dateTo) {
            query.createdAt.$lte = new Date(filters.dateTo);
        }
    }
    if (filters?.paymentMethod) {
        query.paymentMethod = filters.paymentMethod;
    }
    return db.collection('payments').find(query).sort({ createdAt: -1 }).toArray();
}
// READ a specific payment
async function getPaymentById(paymentId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const payment = await db.collection('payments').findOne({ id: paymentId });
    if (!payment) {
        throw new Error('Payment not found');
    }
    // Verify user can access this payment
    if (payment.trainerId !== requestingUserId && payment.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only access payments you are involved in');
    }
    return payment;
}
// UPDATE a payment
async function updatePayment(paymentId, updates, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the payment exists and user has access
    const existingPayment = await getPaymentById(paymentId, requestingUserId);
    // Remove fields that shouldn't be updated directly
    const { _id, id, trainerId, clientId, createdAt, ...safeUpdates } = updates;
    const updateData = {
        ...safeUpdates,
        updatedAt: new Date(),
    };
    const result = await db.collection('payments').updateOne({ id: paymentId }, { $set: updateData });
    return result;
}
// Get trainer earnings/revenue
async function getTrainerRevenue(trainerId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the trainer can access their revenue
    if (trainerId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own revenue data');
    }
    const query = { trainerId };
    // Apply filters
    if (filters?.periodType) {
        query.periodType = filters.periodType;
    }
    if (filters?.dateFrom || filters?.dateTo) {
        query.createdAt = {};
        if (filters.dateFrom) {
            query.createdAt.$gte = new Date(filters.dateFrom);
        }
        if (filters.dateTo) {
            query.createdAt.$lte = new Date(filters.dateTo);
        }
    }
    return db.collection('revenue').find(query).sort({ period: -1 }).toArray();
}
// Calculate and store revenue for a period
async function calculateRevenue(trainerId, period, periodType, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the trainer can calculate their revenue
    if (trainerId !== requestingUserId) {
        throw new Error('Access denied: You can only calculate your own revenue');
    }
    // Get date range for the period
    const { startDate, endDate } = getPeriodDateRange(period, periodType);
    // Aggregate payments for the period
    const pipeline = [
        {
            $match: {
                trainerId,
                status: 'completed',
                paidAt: {
                    $gte: startDate,
                    $lte: endDate
                }
            }
        },
        {
            $group: {
                _id: null,
                totalEarnings: { $sum: '$amount' },
                totalSessions: { $sum: 1 },
                totalClients: { $addToSet: '$clientId' },
                platformFees: { $sum: '$fees.platformFee' },
                processingFees: { $sum: '$fees.processingFee' },
                netEarnings: { $sum: '$fees.trainerEarnings' },
                paymentMethods: { $push: '$paymentMethod' }
            }
        }
    ];
    const result = await db.collection('payments').aggregate(pipeline).toArray();
    if (result.length === 0) {
        return null; // No payments for this period
    }
    const data = result[0];
    // Calculate payment breakdown
    const paymentBreakdown = {
        credit_card: 0,
        paypal: 0,
        cash: 0,
        other: 0
    };
    data.paymentMethods.forEach((method) => {
        if (method === 'credit_card' || method === 'debit_card') {
            paymentBreakdown.credit_card++;
        }
        else if (method === 'paypal') {
            paymentBreakdown.paypal++;
        }
        else if (method === 'cash') {
            paymentBreakdown.cash++;
        }
        else {
            paymentBreakdown.other++;
        }
    });
    const revenueId = new mongodb_2.ObjectId().toString();
    const revenue = {
        id: revenueId,
        trainerId,
        period,
        periodType,
        totalEarnings: data.totalEarnings || 0,
        totalSessions: data.totalSessions || 0,
        totalClients: data.totalClients ? data.totalClients.length : 0,
        averageSessionRate: data.totalSessions > 0 ? Math.round(data.totalEarnings / data.totalSessions) : 0,
        platformFees: data.platformFees || 0,
        processingFees: data.processingFees || 0,
        netEarnings: data.netEarnings || 0,
        paymentBreakdown,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    // Upsert revenue record
    await db.collection('revenue').replaceOne({ trainerId, period, periodType }, revenue, { upsert: true });
    return revenue;
}
// Helper function to get date range for a period
function getPeriodDateRange(period, periodType) {
    const now = new Date();
    let startDate;
    let endDate;
    switch (periodType) {
        case 'daily':
            startDate = new Date(period);
            endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 1);
            break;
        case 'weekly':
            // Period format: "2024-W01"
            const [year, week] = period.split('-W');
            startDate = getDateOfWeek(parseInt(year), parseInt(week));
            endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 7);
            break;
        case 'monthly':
            // Period format: "2024-01"
            const [monthYear, month] = period.split('-');
            startDate = new Date(parseInt(monthYear), parseInt(month) - 1, 1);
            endDate = new Date(parseInt(monthYear), parseInt(month), 1);
            break;
        case 'yearly':
            // Period format: "2024"
            startDate = new Date(parseInt(period), 0, 1);
            endDate = new Date(parseInt(period) + 1, 0, 1);
            break;
        default:
            throw new Error('Invalid period type');
    }
    return { startDate, endDate };
}
// Helper function to get the date of a specific week
function getDateOfWeek(year, week) {
    const simple = new Date(year, 0, 1 + (week - 1) * 7);
    const dow = simple.getDay();
    const ISOweekStart = simple;
    if (dow <= 4) {
        ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1);
    }
    else {
        ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay());
    }
    return ISOweekStart;
}
// Process refund
async function processRefund(paymentId, refundAmount, refundReason, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the payment exists and user has access
    const existingPayment = await getPaymentById(paymentId, requestingUserId);
    // Only trainers can process refunds for their payments
    if (existingPayment.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only trainers can process refunds for their payments');
    }
    if (existingPayment.status !== 'completed') {
        throw new Error('Can only refund completed payments');
    }
    if (refundAmount > existingPayment.amount) {
        throw new Error('Refund amount cannot exceed payment amount');
    }
    const result = await db.collection('payments').updateOne({ id: paymentId }, {
        $set: {
            status: refundAmount === existingPayment.amount ? 'refunded' : 'completed',
            refundAmount,
            refundReason,
            refundedAt: new Date(),
            updatedAt: new Date(),
        }
    });
    return result;
}
//# sourceMappingURL=payments-crud.js.map