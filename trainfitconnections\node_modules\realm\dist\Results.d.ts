import { binding } from "./binding";
import { OrderedCollection } from "./OrderedCollection";
import type { Realm } from "./Realm";
import type { TypeHelpers } from "./TypeHelpers";
import type { Unmanaged } from "./Unmanaged";
import type { ResultsAccessor } from "./collection-accessors/Results";
/**
 * Instances of this class are typically **live** collections returned by
 * objects() that will update as new objects are either
 * added to or deleted from the Realm that match the underlying query. Results returned by
 * snapshot()}, however, will **not** live update
 * (and listener callbacks added through addListener()
 * will thus never be called).
 * @see https://www.mongodb.com/docs/realm/sdk/react-native/model-data/data-types/collections/
 */
export declare class Results<T = unknown> extends OrderedCollection<T, [
    number,
    T
], 
/** @internal */
ResultsAccessor<T>> {
    /**
     * The representation in the binding.
     * @internal
     */
    readonly internal: binding.Results;
    /** @internal */
    subscriptionName?: string;
    /**
     * Create a `Results` wrapping a set of query `Results` from the binding.
     * @internal
     */
    constructor(realm: Realm, internal: binding.Results, accessor: ResultsAccessor<T>, typeHelpers: TypeHelpers<T>);
    /** @internal */
    get(index: number): T;
    /** @internal */
    set(): never;
    get length(): number;
    set length(value: number);
    description(): string;
    /**
     * Bulk update objects in the collection.
     * @param propertyName - The name of the property.
     * @param value - The updated property value.
     * @throws An {@link Error} if no property with the name exists.
     * @since 2.0.0
     */
    update(propertyName: keyof Unmanaged<T>, value: Unmanaged<T>[typeof propertyName]): void;
    /**
     * Checks if this results collection has not been deleted and is part of a valid Realm.
     * @returns `true` if the collection can be safely accessed.
     */
    isValid(): boolean;
    /**
     * Checks if this collection result is empty.
     * @returns `true` if the collection result is empty, `false` if not.
     */
    isEmpty(): boolean;
}
export type AnyResults = Results<any>;
