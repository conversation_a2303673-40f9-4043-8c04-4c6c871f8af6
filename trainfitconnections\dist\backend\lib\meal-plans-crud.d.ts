import { ObjectId } from 'mongodb';
export interface Food {
    id: string;
    name: string;
    quantity: number;
    unit: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    notes?: string;
}
export interface Meal {
    id: string;
    name: string;
    description?: string;
    time: string;
    foods: Food[];
    notes?: string;
    imageUrl?: string;
    type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
    calories?: number;
    macros?: {
        protein: number;
        carbs: number;
        fat: number;
    };
}
export interface MealPlan {
    _id?: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    title: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    startDate: string;
    endDate: string;
    meals: Meal[];
    notes?: string;
    status: 'active' | 'completed' | 'archived';
    calorieTarget?: number;
    macroTargets?: {
        protein: number;
        carbs: number;
        fat: number;
    };
    isTemplate?: boolean;
    tags?: string[];
    dietaryRestrictions?: string[];
}
export declare function createMealPlan(planData: Omit<MealPlan, '_id' | 'id' | 'createdAt' | 'updatedAt'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    title: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    startDate: string;
    endDate: string;
    meals: Meal[];
    notes?: string;
    status: "active" | "completed" | "archived";
    calorieTarget?: number;
    macroTargets?: {
        protein: number;
        carbs: number;
        fat: number;
    };
    isTemplate?: boolean;
    tags?: string[];
    dietaryRestrictions?: string[];
}>;
export declare function getMealPlansForUser(userId: string, requestingUserId: string, filters?: {
    status?: string;
    isTemplate?: boolean;
    dietaryRestrictions?: string[];
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getMealPlanById(planId: string, requestingUserId: string): Promise<MealPlan>;
export declare function updateMealPlan(planId: string, updates: Partial<MealPlan>, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteMealPlan(planId: string, requestingUserId: string): Promise<import("mongodb").DeleteResult>;
export declare function getTrainerMealPlans(trainerId: string, requestingUserId: string, filters?: {
    status?: string;
    isTemplate?: boolean;
    clientId?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getClientMealPlans(clientId: string, requestingUserId: string, filters?: {
    status?: string;
    dietaryRestrictions?: string[];
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function assignMealPlanToClient(planId: string, clientId: string, requestingUserId: string, customizations?: {
    startDate?: string;
    endDate?: string;
    notes?: string;
    calorieTarget?: number;
    macroTargets?: {
        protein: number;
        carbs: number;
        fat: number;
    };
}): Promise<{
    _id: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    title: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    startDate: string;
    endDate: string;
    meals: Meal[];
    notes?: string;
    status: "active" | "completed" | "archived";
    calorieTarget?: number;
    macroTargets?: {
        protein: number;
        carbs: number;
        fat: number;
    };
    isTemplate?: boolean;
    tags?: string[];
    dietaryRestrictions?: string[];
}>;
//# sourceMappingURL=meal-plans-crud.d.ts.map