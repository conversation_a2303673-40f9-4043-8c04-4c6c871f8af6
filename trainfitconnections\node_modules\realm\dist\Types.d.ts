import { BSON } from "./bson";
import * as CounterNS from "./Counter";
import * as ListNS from "./List";
import * as SetNS from "./Set";
import * as DictionaryNS from "./Dictionary";
import * as ResultsNS from "./Results";
declare const GlobalDate: DateConstructor;
type GlobalDate = Date;
export declare namespace Types {
    type Bool = boolean;
    type String = string;
    type Int = number;
    type Float = number;
    type Double = number;
    export import Decimal128 = BSON.Decimal128;
    export import ObjectId = BSON.ObjectId;
    export import UUID = BSON.UUID;
    export import Counter = CounterNS.Counter;
    type Date = GlobalDate;
    const Date: DateConstructor;
    type Data = ArrayBuffer;
    const Data: ArrayBufferConstructor;
    export import List = ListNS.List;
    export import Set = SetNS.RealmSet;
    export import Dictionary = DictionaryNS.Dictionary;
    type Mixed = unknown;
    type LinkingObjects<ObjectTypeT, LinkingPropertyName> = ResultsNS.Results<ObjectTypeT>;
    const LinkingObjects: typeof ResultsNS.Results;
}
export {};
