import type { binding } from "../binding";
import type { ClassHelpers } from "../ClassHelpers";
import type { ListAccessor } from "../collection-accessors/List";
import type { Realm } from "../Realm";
import type { PresentationPropertyTypeName } from "../schema";
import type { TypeHelpers } from "../TypeHelpers";
/** @internal */
export type PropertyContext = binding.Property & {
    type: binding.PropertyType;
    objectSchemaName: string;
    embedded: boolean;
    presentation?: PresentationPropertyTypeName;
    default?: unknown;
};
/** @internal */
export type HelperOptions = {
    realm: Realm;
    getClassHelpers: (name: string) => ClassHelpers;
};
/** @internal */
export type PropertyOptions = {
    typeHelpers: TypeHelpers;
    columnKey: binding.ColKey;
    optional: boolean;
    embedded: boolean;
    presentation?: PresentationPropertyTypeName;
} & HelperOptions & binding.Property_Relaxed;
/** @internal */
export type PropertyAccessor = {
    get(obj: binding.Obj): unknown;
    set(obj: binding.Obj, value: unknown, isCreating?: boolean): unknown;
    listAccessor?: ListAccessor;
};
/** @internal */
export type PropertyHelpers = TypeHelpers & PropertyAccessor & {
    type: binding.PropertyType;
    columnKey: binding.ColKey;
    embedded: boolean;
    default?: unknown;
    objectType?: string;
};
