#!/usr/bin/env node

/**
 * TrainFit Atlas Setup Script
 * Sets up Atlas App Services to replace your local backend server
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 TrainFit Atlas App Services Setup');
console.log('====================================');
console.log('');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Please run this script from your project root directory');
  process.exit(1);
}

console.log('✅ Project directory confirmed');

// Check if Realm SDK is installed
try {
  require.resolve('realm');
  console.log('✅ Realm SDK already installed');
} catch (error) {
  console.log('📦 Installing Realm SDK...');
  try {
    execSync('npm install realm', { stdio: 'inherit' });
    console.log('✅ Realm SDK installed successfully');
  } catch (installError) {
    console.error('❌ Failed to install Realm SDK:', installError.message);
    process.exit(1);
  }
}

// Check if Atlas functions exist
const atlasFunctionsDir = path.join(process.cwd(), 'atlas-functions');
if (!fs.existsSync(atlasFunctionsDir)) {
  console.error('❌ Atlas functions directory not found');
  console.log('Please ensure atlas-functions/ directory exists with function files');
  process.exit(1);
}

const requiredFunctions = ['auth-register.js', 'auth-login.js', 'health-check.js'];
const missingFunctions = requiredFunctions.filter(func => 
  !fs.existsSync(path.join(atlasFunctionsDir, func))
);

if (missingFunctions.length > 0) {
  console.error('❌ Missing function files:', missingFunctions.join(', '));
  process.exit(1);
}

console.log('✅ Atlas function files found');

// Check if Atlas service is set up
const atlasServicePath = path.join(process.cwd(), 'lib', 'atlas-service.ts');
if (!fs.existsSync(atlasServicePath)) {
  console.error('❌ Atlas service file not found: lib/atlas-service.ts');
  process.exit(1);
}

console.log('✅ Atlas service configured');

// Check API configuration
const apiPath = path.join(process.cwd(), 'lib', 'api.ts');
if (!fs.existsSync(apiPath)) {
  console.error('❌ API file not found: lib/api.ts');
  process.exit(1);
}

const apiContent = fs.readFileSync(apiPath, 'utf8');
if (!apiContent.includes('USE_ATLAS_SDK')) {
  console.error('❌ API file not configured for Atlas SDK');
  console.log('Please ensure lib/api.ts includes Atlas SDK integration');
  process.exit(1);
}

console.log('✅ API configured for Atlas SDK');

console.log('');
console.log('🎯 Setup Complete! Next Steps:');
console.log('');
console.log('📋 Option 1: Automatic Deployment (Recommended)');
console.log('1. Get Atlas API keys from:');
console.log('   https://cloud.mongodb.com/v2#/org/685878c01ee071769a3fc029/access/apiKeys');
console.log('2. Set environment variables:');
console.log('   export ATLAS_PUBLIC_KEY="your-public-key"');
console.log('   export ATLAS_PRIVATE_KEY="your-private-key"');
console.log('3. Run: node scripts/deploy-atlas-functions.js');
console.log('');
console.log('📋 Option 2: Manual Setup');
console.log('1. Go to Atlas App Services:');
console.log('   https://services.cloud.mongodb.com/groups/685878c11ee071769a3fc035/apps/685b8f784722bb4bb71bb804/functions');
console.log('2. Create functions using code from atlas-functions/ directory');
console.log('3. Create HTTPS endpoints for each function');
console.log('4. Deploy your app');
console.log('');
console.log('🔗 Your Atlas App Services URL:');
console.log('   https://data.mongodb-api.com/app/trainfit-backend-sjcdpns/endpoint');
console.log('');
console.log('⚡ Once deployed:');
console.log('• Stop your local backend server (localhost:3000)');
console.log('• Your mobile app will use Atlas App Services automatically');
console.log('• No more "network request failed" errors!');
console.log('• Same MongoDB database, same collections');
console.log('');
console.log('✨ Your TrainFit app will work for real users worldwide!');
