import { Context, Next } from 'hono';
import { verifyToken, getUserById, AuthTokenPayload } from '../lib/auth';

export interface AuthContext {
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
  };
}

// Authentication middleware
export async function authMiddleware(c: Context, next: Next) {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ error: 'Missing or invalid authorization header' }, 401);
  }
  
  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  
  // Verify token
  const payload = verifyToken(token);
  if (!payload) {
    return c.json({ error: 'Invalid or expired token' }, 401);
  }
  
  // Get user from database
  const user = await getUserById(payload.userId);
  if (!user) {
    return c.json({ error: 'User not found' }, 401);
  }
  
  // Add user to context
  c.set('user', {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
  });
  
  await next();
}

// Optional authentication middleware (doesn't fail if no token)
export async function optionalAuthMiddleware(c: Context, next: Next) {
  const authHeader = c.req.header('Authorization');
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    const payload = verifyToken(token);
    
    if (payload) {
      const user = await getUserById(payload.userId);
      if (user) {
        c.set('user', {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        });
      }
    }
  }
  
  await next();
}

// Role-based authorization middleware
export function requireRole(allowedRoles: string[]) {
  return async (c: Context, next: Next) => {
    const user = c.get('user');
    
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }
    
    if (!allowedRoles.includes(user.role)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }
    
    await next();
  };
}

// Helper to get current user from context
export function getCurrentUser(c: Context): AuthContext['user'] | null {
  return c.get('user') || null;
}
