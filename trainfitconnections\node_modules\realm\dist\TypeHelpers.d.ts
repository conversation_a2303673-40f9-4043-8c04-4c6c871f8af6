import { binding } from "./binding";
/** @internal */
import type { TypeHelpers, TypeOptions } from "./type-helpers/types";
/** @internal */
export type { TypeHelpers, TypeOptions };
/** @internal */
export type MappableTypeHelpers = Exclude<binding.PropertyType, binding.PropertyType.Nullable | binding.PropertyType.Collection | binding.PropertyType.Flags>;
/** @internal */
export declare function toItemType(type: binding.PropertyType): number;
/** @internal */
export declare function getTypeHelpers(type: MappableTypeHelpers, options: TypeOptions): TypeHelpers;
