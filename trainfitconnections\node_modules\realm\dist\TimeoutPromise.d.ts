export type TimeoutPromiseOptions = {
    ms?: number;
    message?: string;
    rejectOnTimeout?: boolean;
};
export declare class TimeoutPromise<T = unknown> implements Promise<T | void> {
    private timer;
    private handle;
    constructor(inner: Promise<T>, { ms, message, rejectOnTimeout }?: TimeoutPromiseOptions);
    cancel(): void;
    then: <TResult1 = void | T, TResult2 = never>(onfulfilled?: ((value: void | T) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null | undefined) => Promise<TResult1 | TResult2>;
    catch: <TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null | undefined) => Promise<void | T | TResult>;
    finally: (onfinally?: (() => void) | null | undefined) => Promise<void | T>;
    get [Symbol.toStringTag](): string;
}
