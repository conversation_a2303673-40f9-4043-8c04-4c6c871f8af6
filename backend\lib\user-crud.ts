import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

// CREATE a user
export async function createUser(user: any) {
  const client = await clientPromise;
  const db = client.db(dbName);
  const result = await db.collection('users').insertOne(user);
  return result.insertedId;
}

// READ a user by id
export async function getUserById(id: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  return db.collection('users').findOne({ id });
}

// UPDATE a user by id
export async function updateUserById(id: string, update: any) {
  const client = await clientPromise;
  const db = client.db(dbName);
  return db.collection('users').updateOne({ id }, { $set: update });
}

// DELETE a user by id
export async function deleteUserById(id: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  return db.collection('users').deleteOne({ id });
}
