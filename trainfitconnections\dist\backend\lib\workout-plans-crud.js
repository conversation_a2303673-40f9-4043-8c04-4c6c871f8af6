"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWorkoutPlan = createWorkoutPlan;
exports.getWorkoutPlansForUser = getWorkoutPlansForUser;
exports.getWorkoutPlanById = getWorkoutPlanById;
exports.updateWorkoutPlan = updateWorkoutPlan;
exports.deleteWorkoutPlan = deleteWorkoutPlan;
exports.getTrainerWorkoutPlans = getTrainerWorkoutPlans;
exports.getClientWorkoutPlans = getClientWorkoutPlans;
exports.assignWorkoutPlanToClient = assignWorkoutPlanToClient;
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const dbName = 'trainfitconnections';
// CREATE a workout plan
async function createWorkoutPlan(planData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only trainers can create workout plans
    if (planData.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only trainers can create workout plans');
    }
    const planId = new mongodb_2.ObjectId().toString();
    const workoutPlan = {
        ...planData,
        id: planId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('workout_plans').insertOne(workoutPlan);
    return { ...workoutPlan, _id: result.insertedId };
}
// READ workout plans for a user
async function getWorkoutPlansForUser(userId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own workout plans
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own workout plans');
    }
    const query = {
        $or: [
            { trainerId: userId },
            { clientId: userId }
        ]
    };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.difficulty) {
        query.difficulty = filters.difficulty;
    }
    if (filters?.category) {
        query.category = filters.category;
    }
    if (filters?.isTemplate !== undefined) {
        query.isTemplate = filters.isTemplate;
    }
    return db.collection('workout_plans').find(query).sort({ createdAt: -1 }).toArray();
}
// READ a specific workout plan
async function getWorkoutPlanById(planId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const plan = await db.collection('workout_plans').findOne({ id: planId });
    if (!plan) {
        throw new Error('Workout plan not found');
    }
    // Verify user can access this plan
    if (plan.trainerId !== requestingUserId && plan.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only access workout plans assigned to you');
    }
    return plan;
}
// UPDATE a workout plan
async function updateWorkoutPlan(planId, updates, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the plan exists and user has access
    const existingPlan = await getWorkoutPlanById(planId, requestingUserId);
    // Only trainers can update workout plans
    if (existingPlan.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only the trainer who created this plan can update it');
    }
    // Remove fields that shouldn't be updated directly
    const { _id, id, createdAt, ...safeUpdates } = updates;
    const updateData = {
        ...safeUpdates,
        updatedAt: new Date(),
    };
    const result = await db.collection('workout_plans').updateOne({ id: planId }, { $set: updateData });
    return result;
}
// DELETE a workout plan
async function deleteWorkoutPlan(planId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the plan exists and user has access
    const existingPlan = await getWorkoutPlanById(planId, requestingUserId);
    // Only trainers can delete workout plans
    if (existingPlan.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only the trainer who created this plan can delete it');
    }
    const result = await db.collection('workout_plans').deleteOne({ id: planId });
    return result;
}
// Get workout plans created by a trainer
async function getTrainerWorkoutPlans(trainerId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the trainer can access their workout plans
    if (trainerId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own workout plans');
    }
    const query = { trainerId };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.isTemplate !== undefined) {
        query.isTemplate = filters.isTemplate;
    }
    if (filters?.clientId) {
        query.clientId = filters.clientId;
    }
    return db.collection('workout_plans').find(query).sort({ createdAt: -1 }).toArray();
}
// Get workout plans assigned to a client
async function getClientWorkoutPlans(clientId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the client can access their workout plans
    if (clientId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own workout plans');
    }
    const query = { clientId };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.difficulty) {
        query.difficulty = filters.difficulty;
    }
    return db.collection('workout_plans').find(query).sort({ createdAt: -1 }).toArray();
}
// Assign a workout plan to a client (copy from template or assign existing)
async function assignWorkoutPlanToClient(planId, clientId, requestingUserId, customizations) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Get the original plan
    const originalPlan = await getWorkoutPlanById(planId, requestingUserId);
    // Only trainers can assign workout plans
    if (originalPlan.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only trainers can assign workout plans');
    }
    // Create a new plan for the client
    const newPlanId = new mongodb_2.ObjectId().toString();
    const assignedPlan = {
        ...originalPlan,
        id: newPlanId,
        clientId,
        isTemplate: false,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        ...customizations,
    };
    // Remove the _id to create a new document
    delete assignedPlan._id;
    const result = await db.collection('workout_plans').insertOne(assignedPlan);
    return { ...assignedPlan, _id: result.insertedId };
}
//# sourceMappingURL=workout-plans-crud.js.map