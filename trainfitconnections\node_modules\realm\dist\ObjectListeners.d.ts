import { binding } from "./binding";
import type { RealmObject } from "./Object";
export type ObjectChangeSet<T> = {
    /**
     * Is `true` if the object has been deleted.
     */
    deleted: boolean;
    /**
     * An array of properties that have changed their value.
     */
    changedProperties: (keyof T)[];
};
export type ObjectChangeCallback<T> = (
/**
 * The object that changed.
 */
object: RealmObject<T> & T, 
/**
 * A dictionary with information about the changes.
 */
changes: ObjectChangeSet<T>) => void;
/** @internal */
export declare class ObjectListeners<T> {
    private realm;
    private object;
    /**
     * Storage for the memoized, lazily created object notifier.
     */
    private internal;
    constructor(realm: binding.Realm, object: RealmObject<T>);
    private properties;
    private listeners;
    /**
     * A memoized, lazily created object notifier.
     */
    private get notifier();
    addListener(callback: ObjectChangeCallback<T>, keyPaths: undefined | string[]): void;
    removeListener(callback: ObjectChangeCallback<T>): void;
    removeAllListeners(): void;
    private mapKeyPaths;
}
