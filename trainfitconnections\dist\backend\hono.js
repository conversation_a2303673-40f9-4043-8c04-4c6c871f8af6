"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.app = void 0;
const hono_1 = require("hono");
const cors_1 = require("hono/cors");
const trpc_server_1 = require("@hono/trpc-server");
const app_router_1 = require("./trpc/app-router");
const trpc_1 = require("./trpc/trpc");
const user_crud_1 = require("./lib/user-crud");
const auth_1 = require("./routes/auth");
const auth_2 = require("./middleware/auth");
// Import all CRUD operations
const sessionsCrud = __importStar(require("./lib/sessions-crud"));
const workoutPlansCrud = __importStar(require("./lib/workout-plans-crud"));
const mealPlansCrud = __importStar(require("./lib/meal-plans-crud"));
const messagesCrud = __importStar(require("./lib/messages-crud"));
const mediaCrud = __importStar(require("./lib/media-crud"));
const notificationsCrud = __importStar(require("./lib/notifications-crud"));
const app = new hono_1.Hono();
exports.app = app;
// Enable CORS
app.use("/*", (0, cors_1.cors)({
    origin: "*",
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    maxAge: 86400,
}));
// Add request size limit middleware - IMPROVED ERROR HANDLING
app.use("*", async (c, next) => {
    const contentLength = c.req.header('content-length');
    // If content length is provided, check if it exceeds the limit (10MB)
    if (contentLength) {
        const size = parseInt(contentLength, 10);
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (size > maxSize) {
            console.error(`Request payload too large: ${size} bytes (max: ${maxSize} bytes)`);
            return c.json({
                error: "Payload too large",
                message: "The request payload exceeds the maximum allowed size of 10MB.",
                details: {
                    requestSize: `${(size / (1024 * 1024)).toFixed(2)}MB`,
                    maxAllowedSize: "10MB"
                }
            }, 413);
        }
    }
    try {
        await next();
    }
    catch (error) {
        // Check if the error is related to payload size
        if (error instanceof Error &&
            (error.message.includes('payload') ||
                error.message.includes('size') ||
                error.message.includes('large'))) {
            console.error('Payload size error:', error);
            return c.json({
                error: "Payload too large",
                message: error.message || "The request payload is too large.",
                details: {
                    maxAllowedSize: "10MB"
                }
            }, 413);
        }
        // Re-throw other errors
        throw error;
    }
});
// Add request timeout handling
app.use("*", async (c, next) => {
    try {
        // Set a timeout for the request
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error("Request timeout"));
            }, 25000); // 25 second timeout
        });
        // Race the request against the timeout
        await Promise.race([next(), timeoutPromise]);
    }
    catch (error) {
        if (error instanceof Error && error.message === "Request timeout") {
            return c.json({ error: "Request timeout" }, 504);
        }
        throw error;
    }
});
// Mount tRPC router using trpcServer middleware
app.use("/api/trpc/*", (0, trpc_server_1.trpcServer)({
    router: app_router_1.appRouter,
    createContext: trpc_1.createContext,
    onError: (opts) => {
        const { error, type, path, input, ctx, req } = opts;
        console.error(`Error in tRPC handler: ${path}`, {
            type,
            input,
            error: error.message,
            stack: error.stack,
        });
        // Check if the error is related to payload size
        if (error.message.includes("payload") ||
            error.message.includes("size") ||
            error.message.includes("large")) {
            return {
                message: `Payload too large: ${error.message}`,
                code: "PAYLOAD_TOO_LARGE",
            };
        }
        // Return a more helpful error message
        return {
            message: `An error occurred: ${error.message}`,
            code: "INTERNAL_SERVER_ERROR",
        };
    },
}));
// Add a simple health check endpoint
app.get("/api/health", (c) => {
    return c.json({
        status: "ok",
        timestamp: new Date().toISOString(),
    });
});
// Mount authentication routes
app.route('/api/auth', auth_1.auth);
// Protected user endpoints
app.get('/api/users/me', auth_2.authMiddleware, async (c) => {
    const user = (0, auth_2.getCurrentUser)(c);
    if (!user)
        return c.json({ error: 'User not found' }, 404);
    try {
        const userData = await (0, user_crud_1.getUserById)(user.id, user.id);
        return c.json(userData);
    }
    catch (error) {
        return c.json({ error: 'Failed to fetch user data' }, 500);
    }
});
app.put('/api/users/me', auth_2.authMiddleware, async (c) => {
    const user = (0, auth_2.getCurrentUser)(c);
    if (!user)
        return c.json({ error: 'User not found' }, 401);
    try {
        const update = await c.req.json();
        const result = await (0, user_crud_1.updateUserById)(user.id, update, user.id);
        return c.json({ modifiedCount: result.modifiedCount });
    }
    catch (error) {
        return c.json({
            error: error instanceof Error ? error.message : 'Update failed'
        }, 400);
    }
});
app.delete('/api/users/me', auth_2.authMiddleware, async (c) => {
    const user = (0, auth_2.getCurrentUser)(c);
    if (!user)
        return c.json({ error: 'User not found' }, 401);
    try {
        const result = await (0, user_crud_1.deleteUserById)(user.id, user.id);
        return c.json({ deletedCount: result.deletedCount });
    }
    catch (error) {
        return c.json({
            error: error instanceof Error ? error.message : 'Delete failed'
        }, 400);
    }
});
// Public endpoint to get trainers list
app.get('/api/trainers', async (c) => {
    try {
        const trainers = await (0, user_crud_1.getTrainers)();
        return c.json(trainers);
    }
    catch (error) {
        return c.json({ error: 'Failed to fetch trainers' }, 500);
    }
});
// ===== SESSIONS API ROUTES =====
app.post('/api/sessions', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const sessionData = await c.req.json();
        const session = await sessionsCrud.createSession(sessionData, user.id);
        return c.json(session, 201);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to create session' }, 400);
    }
});
app.get('/api/sessions', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const filters = {
            status: c.req.query('status'),
            dateFrom: c.req.query('dateFrom'),
            dateTo: c.req.query('dateTo'),
            type: c.req.query('type'),
        };
        const sessions = await sessionsCrud.getSessionsForUser(user.id, user.id, filters);
        return c.json(sessions);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch sessions' }, 400);
    }
});
app.get('/api/sessions/:id', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const sessionId = c.req.param('id');
        const session = await sessionsCrud.getSessionById(sessionId, user.id);
        return c.json(session);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch session' }, 400);
    }
});
app.put('/api/sessions/:id', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const sessionId = c.req.param('id');
        const updates = await c.req.json();
        const result = await sessionsCrud.updateSession(sessionId, updates, user.id);
        return c.json({ modifiedCount: result.modifiedCount });
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to update session' }, 400);
    }
});
app.delete('/api/sessions/:id', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const sessionId = c.req.param('id');
        const result = await sessionsCrud.deleteSession(sessionId, user.id);
        return c.json({ deletedCount: result.deletedCount });
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to delete session' }, 400);
    }
});
// ===== WORKOUT PLANS API ROUTES =====
app.post('/api/workout-plans', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const planData = await c.req.json();
        const plan = await workoutPlansCrud.createWorkoutPlan(planData, user.id);
        return c.json(plan, 201);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to create workout plan' }, 400);
    }
});
app.get('/api/workout-plans', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const filters = {
            status: c.req.query('status'),
            difficulty: c.req.query('difficulty'),
            category: c.req.query('category'),
            isTemplate: c.req.query('isTemplate') === 'true',
        };
        const plans = await workoutPlansCrud.getWorkoutPlansForUser(user.id, user.id, filters);
        return c.json(plans);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch workout plans' }, 400);
    }
});
app.get('/api/workout-plans/:id', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const planId = c.req.param('id');
        const plan = await workoutPlansCrud.getWorkoutPlanById(planId, user.id);
        return c.json(plan);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch workout plan' }, 400);
    }
});
app.put('/api/workout-plans/:id', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const planId = c.req.param('id');
        const updates = await c.req.json();
        const result = await workoutPlansCrud.updateWorkoutPlan(planId, updates, user.id);
        return c.json({ modifiedCount: result.modifiedCount });
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to update workout plan' }, 400);
    }
});
app.delete('/api/workout-plans/:id', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const planId = c.req.param('id');
        const result = await workoutPlansCrud.deleteWorkoutPlan(planId, user.id);
        return c.json({ deletedCount: result.deletedCount });
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to delete workout plan' }, 400);
    }
});
// ===== MEAL PLANS API ROUTES =====
app.post('/api/meal-plans', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const planData = await c.req.json();
        const plan = await mealPlansCrud.createMealPlan(planData, user.id);
        return c.json(plan, 201);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to create meal plan' }, 400);
    }
});
app.get('/api/meal-plans', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const filters = {
            status: c.req.query('status'),
            isTemplate: c.req.query('isTemplate') === 'true',
            dietaryRestrictions: c.req.query('dietaryRestrictions')?.split(','),
        };
        const plans = await mealPlansCrud.getMealPlansForUser(user.id, user.id, filters);
        return c.json(plans);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch meal plans' }, 400);
    }
});
app.get('/api/meal-plans/:id', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const planId = c.req.param('id');
        const plan = await mealPlansCrud.getMealPlanById(planId, user.id);
        return c.json(plan);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch meal plan' }, 400);
    }
});
// ===== MESSAGES API ROUTES =====
app.post('/api/messages', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const messageData = await c.req.json();
        const message = await messagesCrud.createMessage(messageData, user.id);
        return c.json(message, 201);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to create message' }, 400);
    }
});
app.get('/api/conversations', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const conversations = await messagesCrud.getConversationsForUser(user.id, user.id);
        return c.json(conversations);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch conversations' }, 400);
    }
});
app.get('/api/conversations/:id/messages', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const conversationId = c.req.param('id');
        const options = {
            limit: parseInt(c.req.query('limit') || '50'),
            offset: parseInt(c.req.query('offset') || '0'),
        };
        const messages = await messagesCrud.getMessagesForConversation(conversationId, user.id, options);
        return c.json(messages);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch messages' }, 400);
    }
});
// ===== NOTIFICATIONS API ROUTES =====
app.get('/api/notifications', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const filters = {
            isRead: c.req.query('isRead') === 'true',
            type: c.req.query('type'),
            priority: c.req.query('priority'),
            limit: parseInt(c.req.query('limit') || '50'),
            offset: parseInt(c.req.query('offset') || '0'),
        };
        const notifications = await notificationsCrud.getNotificationsForUser(user.id, user.id, filters);
        return c.json(notifications);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch notifications' }, 400);
    }
});
app.put('/api/notifications/:id/read', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const notificationId = c.req.param('id');
        const result = await notificationsCrud.markNotificationAsRead(notificationId, user.id);
        return c.json({ modifiedCount: result.modifiedCount });
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to mark notification as read' }, 400);
    }
});
app.get('/api/notifications/unread-count', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const count = await notificationsCrud.getUnreadNotificationCount(user.id, user.id);
        return c.json({ count });
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to get unread count' }, 400);
    }
});
// ===== MEDIA API ROUTES =====
app.post('/api/media', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const fileData = await c.req.json();
        const file = await mediaCrud.createMediaFile(fileData, user.id);
        return c.json(file, 201);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to upload media' }, 400);
    }
});
app.get('/api/media', auth_2.authMiddleware, async (c) => {
    try {
        const user = (0, auth_2.getCurrentUser)(c);
        const filters = {
            type: c.req.query('type'),
            category: c.req.query('category'),
            isPublic: c.req.query('isPublic') === 'true',
        };
        const files = await mediaCrud.getMediaFilesForUser(user.id, user.id, filters);
        return c.json(files);
    }
    catch (error) {
        return c.json({ error: error instanceof Error ? error.message : 'Failed to fetch media files' }, 400);
    }
});
// Add a catch-all route for debugging
app.all("*", (c) => {
    const method = c.req.method;
    const path = c.req.path;
    const headers = Object.fromEntries([...c.req.raw.headers.entries()]);
    console.log(`Unhandled request: ${method} ${path}`);
    console.log("Headers:", headers);
    return c.json({
        error: "Not found",
        message: `No handler for ${method} ${path}`,
        requestInfo: {
            method,
            path,
            headers: {
                ...headers,
                // Remove sensitive headers
                authorization: headers.authorization ? "[REDACTED]" : undefined,
                cookie: headers.cookie ? "[REDACTED]" : undefined,
            }
        }
    }, 404);
});
//# sourceMappingURL=hono.js.map