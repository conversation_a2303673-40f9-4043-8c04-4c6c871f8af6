"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMediaFile = createMediaFile;
exports.getMediaFilesForUser = getMediaFilesForUser;
exports.getMediaFileById = getMediaFileById;
exports.updateMediaFile = updateMediaFile;
exports.deleteMediaFile = deleteMediaFile;
exports.shareMediaFile = shareMediaFile;
exports.createProgressPhoto = createProgressPhoto;
exports.getProgressPhotosForClient = getProgressPhotosForClient;
exports.updateProgressPhoto = updateProgressPhoto;
exports.deleteProgressPhoto = deleteProgressPhoto;
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const dbName = 'trainfitconnections';
// CREATE a media file
async function createMediaFile(fileData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Verify user can upload this file (must be the owner)
    if (fileData.ownerId !== requestingUserId) {
        throw new Error('Access denied: You can only upload files as yourself');
    }
    const fileId = new mongodb_2.ObjectId().toString();
    const mediaFile = {
        ...fileData,
        id: fileId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('media_files').insertOne(mediaFile);
    return { ...mediaFile, _id: result.insertedId };
}
// READ media files for a user
async function getMediaFilesForUser(userId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    let query = {
        isDeleted: { $ne: true }
    };
    // Users can access:
    // 1. Their own files
    // 2. Public files
    // 3. Files shared with them
    if (userId === requestingUserId) {
        // Own files
        query.ownerId = userId;
    }
    else {
        // Public files or files shared with requesting user
        query.$or = [
            { isPublic: true, ownerId: userId },
            { sharedWith: requestingUserId, ownerId: userId }
        ];
    }
    // Apply filters
    if (filters?.type) {
        query.type = filters.type;
    }
    if (filters?.category) {
        query.category = filters.category;
    }
    if (filters?.isPublic !== undefined) {
        query.isPublic = filters.isPublic;
    }
    return db.collection('media_files').find(query).sort({ createdAt: -1 }).toArray();
}
// READ a specific media file
async function getMediaFileById(fileId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const file = await db.collection('media_files').findOne({
        id: fileId,
        isDeleted: { $ne: true }
    });
    if (!file) {
        throw new Error('Media file not found');
    }
    // Check access permissions
    const hasAccess = file.ownerId === requestingUserId || // Owner
        file.isPublic || // Public file
        (file.sharedWith && file.sharedWith.includes(requestingUserId)); // Shared with user
    if (!hasAccess) {
        throw new Error('Access denied: You do not have permission to access this file');
    }
    return file;
}
// UPDATE a media file
async function updateMediaFile(fileId, updates, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the file exists and user has access
    const existingFile = await getMediaFileById(fileId, requestingUserId);
    // Only the owner can update the file
    if (existingFile.ownerId !== requestingUserId) {
        throw new Error('Access denied: Only the file owner can update this file');
    }
    // Remove fields that shouldn't be updated directly
    const { _id, id, ownerId, createdAt, ...safeUpdates } = updates;
    const updateData = {
        ...safeUpdates,
        updatedAt: new Date(),
    };
    const result = await db.collection('media_files').updateOne({ id: fileId }, { $set: updateData });
    return result;
}
// DELETE a media file (soft delete)
async function deleteMediaFile(fileId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the file exists and user has access
    const existingFile = await getMediaFileById(fileId, requestingUserId);
    // Only the owner can delete the file
    if (existingFile.ownerId !== requestingUserId) {
        throw new Error('Access denied: Only the file owner can delete this file');
    }
    const result = await db.collection('media_files').updateOne({ id: fileId }, {
        $set: {
            isDeleted: true,
            deletedAt: new Date(),
            updatedAt: new Date(),
        }
    });
    return result;
}
// Share a media file with specific users
async function shareMediaFile(fileId, userIds, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the file exists and user has access
    const existingFile = await getMediaFileById(fileId, requestingUserId);
    // Only the owner can share the file
    if (existingFile.ownerId !== requestingUserId) {
        throw new Error('Access denied: Only the file owner can share this file');
    }
    const result = await db.collection('media_files').updateOne({ id: fileId }, {
        $addToSet: { sharedWith: { $each: userIds } },
        $set: { updatedAt: new Date() }
    });
    return result;
}
// CREATE a progress photo
async function createProgressPhoto(photoData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Verify user can create this progress photo (must be the client)
    if (photoData.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only create progress photos for yourself');
    }
    const photoId = new mongodb_2.ObjectId().toString();
    const progressPhoto = {
        ...photoData,
        id: photoId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('progress_photos').insertOne(progressPhoto);
    return { ...progressPhoto, _id: result.insertedId };
}
// READ progress photos for a client
async function getProgressPhotosForClient(clientId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can access progress photos if:
    // 1. They are the client
    // 2. They are the trainer and photos are visible
    let query = { clientId };
    if (clientId !== requestingUserId) {
        // If not the client, must be trainer and photos must be visible
        query.trainerId = requestingUserId;
        query.isVisible = true;
    }
    // Apply filters
    if (filters?.dateFrom || filters?.dateTo) {
        query.date = {};
        if (filters.dateFrom) {
            query.date.$gte = filters.dateFrom;
        }
        if (filters.dateTo) {
            query.date.$lte = filters.dateTo;
        }
    }
    if (filters?.isVisible !== undefined && clientId === requestingUserId) {
        query.isVisible = filters.isVisible;
    }
    return db.collection('progress_photos').find(query).sort({ date: -1 }).toArray();
}
// UPDATE a progress photo
async function updateProgressPhoto(photoId, updates, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const existingPhoto = await db.collection('progress_photos').findOne({ id: photoId });
    if (!existingPhoto) {
        throw new Error('Progress photo not found');
    }
    // Only the client can update their progress photo
    if (existingPhoto.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only update your own progress photos');
    }
    // Remove fields that shouldn't be updated directly
    const { _id, id, clientId, createdAt, ...safeUpdates } = updates;
    const updateData = {
        ...safeUpdates,
        updatedAt: new Date(),
    };
    const result = await db.collection('progress_photos').updateOne({ id: photoId }, { $set: updateData });
    return result;
}
// DELETE a progress photo
async function deleteProgressPhoto(photoId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const existingPhoto = await db.collection('progress_photos').findOne({ id: photoId });
    if (!existingPhoto) {
        throw new Error('Progress photo not found');
    }
    // Only the client can delete their progress photo
    if (existingPhoto.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only delete your own progress photos');
    }
    const result = await db.collection('progress_photos').deleteOne({ id: photoId });
    return result;
}
//# sourceMappingURL=media-crud.js.map