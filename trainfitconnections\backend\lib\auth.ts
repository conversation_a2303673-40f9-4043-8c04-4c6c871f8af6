import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = '7d';
const SALT_ROUNDS = 12;

export interface User {
  _id?: ObjectId;
  id: string;
  email: string;
  name: string;
  role: 'client' | 'trainer';
  passwordHash: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthTokenPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS);
}

// Verify password
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// Generate JWT token
export function generateToken(user: User): string {
  const payload: AuthTokenPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
  };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

// Verify JWT token
export function verifyToken(token: string): AuthTokenPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as AuthTokenPayload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// Register new user
export async function registerUser(
  email: string, 
  password: string, 
  name: string, 
  role: 'client' | 'trainer'
): Promise<{ user: User; token: string }> {
  const client = await clientPromise;
  const db = client.db('trainfitconnections');
  
  // Check if user already exists
  const existingUser = await db.collection('users').findOne({ email: email.toLowerCase() });
  if (existingUser) {
    throw new Error('User with this email already exists');
  }
  
  // Hash password
  const passwordHash = await hashPassword(password);
  
  // Create user
  const userId = new ObjectId().toString();
  const user: User = {
    id: userId,
    email: email.toLowerCase(),
    name,
    role,
    passwordHash,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  await db.collection('users').insertOne(user);
  
  // Generate token
  const token = generateToken(user);
  
  // Remove password hash from returned user
  const { passwordHash: _, ...userWithoutPassword } = user;
  
  return { user: userWithoutPassword as User, token };
}

// Login user
export async function loginUser(
  email: string, 
  password: string
): Promise<{ user: User; token: string }> {
  const client = await clientPromise;
  const db = client.db('trainfitconnections');
  
  // Find user
  const user = await db.collection('users').findOne({ email: email.toLowerCase() }) as User | null;
  if (!user) {
    throw new Error('Invalid email or password');
  }
  
  // Verify password
  const isValidPassword = await verifyPassword(password, user.passwordHash);
  if (!isValidPassword) {
    throw new Error('Invalid email or password');
  }
  
  // Generate token
  const token = generateToken(user);
  
  // Remove password hash from returned user
  const { passwordHash: _, ...userWithoutPassword } = user;
  
  return { user: userWithoutPassword as User, token };
}

// Get user by ID
export async function getUserById(userId: string): Promise<User | null> {
  const client = await clientPromise;
  const db = client.db('trainfitconnections');
  
  const user = await db.collection('users').findOne({ id: userId }) as User | null;
  if (user) {
    const { passwordHash: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }
  
  return null;
}
