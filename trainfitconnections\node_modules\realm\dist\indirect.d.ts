import type { Realm } from "./Realm";
import type { Results } from "./Results";
import type { List } from "./List";
import type { Dictionary } from "./Dictionary";
import type { RealmSet } from "./Set";
import type { OrderedCollection } from "./OrderedCollection";
import type { RealmObject } from "./Object";
import type { Collection } from "./Collection";
type Indirects = {
    Realm: typeof Realm;
    Collection: typeof Collection;
    OrderedCollection: typeof OrderedCollection;
    Results: typeof Results;
    List: typeof List;
    Dictionary: typeof Dictionary;
    Set: typeof RealmSet;
    Object: typeof RealmObject;
};
/**
 * Values that can be dependent on at runtime without eagerly loading it into the module.
 * Use this as a last resort only to break cyclic imports.
 * @internal
 */
export declare const indirect: Indirects;
/**
 * Injects a value that can be dependent on at runtime without eagerly loading it into the module.
 * @internal
 */
export declare function injectIndirect<Name extends keyof typeof indirect>(name: Name, value: (typeof indirect)[typeof name]): void;
export {};
