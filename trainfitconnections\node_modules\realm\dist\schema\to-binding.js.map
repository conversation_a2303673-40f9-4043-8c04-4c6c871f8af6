{"version": 3, "file": "to-binding.js", "sourceRoot": "", "sources": ["../../src/schema/to-binding.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;AAE5E,sCAAmC;AASnC,gBAAgB;AACH,QAAA,aAAa,GAAkD;IAC1E,GAAG,iCAAyB;IAC5B,IAAI,kCAA0B;IAC9B,MAAM,oCAA4B;IAClC,IAAI,kCAA0B;IAC9B,IAAI,kCAA0B;IAC9B,KAAK,mCAA2B;IAChC,MAAM,oCAA4B;IAClC,KAAK,mCAA2B;IAChC,QAAQ,uCAA8B;IACtC,UAAU,sCAA6B;IACvC,IAAI,mCAA0B;IAC9B,IAAI,qCAA2B;IAC/B,GAAG,mCAAyB;IAC5B,UAAU,0CAAgC;IAC1C,cAAc,4CAAoC;IAClD,MAAM,oCAA4B;CACnC,CAAC;AAEF,SAAS,eAAe,CAAC,MAA6B;IACpD,IAAI,MAAM,CAAC,QAAQ,EAAE;QACnB,eAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,IAAI,0CAA0C,CAAC,CAAC;QAC7F,kCAA0B;KAC3B;SAAM,IAAI,MAAM,CAAC,UAAU,EAAE;QAC5B,4CAAoC;KACrC;SAAM;QACL,kCAA0B;KAC3B;AACH,CAAC;AAED,gBAAgB;AAChB,SAAgB,eAAe,CAAC,MAA+B;IAC7D,OAAO,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAC3C,CAAC;AAFD,0CAEC;AAED,gBAAgB;AAChB,SAAgB,qBAAqB,CAAC,MAA6B;IACjE,wDAAwD;IACxD,6CAA6C;IAC7C,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC;SACjD,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAClE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;QAChB,oDAAoD;QACpD,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAC,UAAU,EAAE;YACvC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;SAC3B;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC,CAAC;IACL,MAAM,MAAM,GAAwB;QAClC,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,SAAS,EAAE,eAAe,CAAC,MAAM,CAAC;QAClC,mBAAmB,EAAE,UAAU,CAAC,MAAM,CACpC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,oCAA0B,CAAC,+CAAuC,CACpF;QACD,kBAAkB,EAAE,UAAU,CAAC,MAAM,CACnC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,oCAA0B,CAAC,+CAAuC,CACpF;KACF,CAAC;IACF,sEAAsE;IACtE,IAAI,MAAM,CAAC,UAAU,EAAE;QACrB,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;KACvC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AA3BD,sDA2BC;AAED,gBAAgB;AAChB,SAAgB,uBAAuB,CAAC,IAAY,EAAE,MAA+B;IACnF,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;QACxB,2EAA2E;QAC3E,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;KACvE;IACD,MAAM,MAAM,GAAoB;QAC9B,IAAI;QACJ,IAAI,EAAE,qBAAqB,CAAC,MAAM,CAAC;QACnC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,qBAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU;QACnG,sBAAsB,EAAE,MAAM,CAAC,QAAQ;KACxC,CAAC;IAEF,IAAI,MAAM,CAAC,OAAO,KAAK,WAAW,EAAE;QAClC,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;KACjC;SAAM;QACL,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;KACnC;IAED,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,EAAE;QAChD,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;QAChC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;KAC5B;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAvBD,0DAuBC;AAED,gBAAgB;AAChB,SAAgB,qBAAqB,CAAC,MAA+B;IACnE,IAAI,IAAI,GAAG,qBAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtC,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;IACjC,IAAI,IAAI,+CAAuC,EAAE;QAC/C,OAAO,IAAI,sCAA4B,CAAC;KACzC;SAAM,IAAI,MAAM,CAAC,UAAU,EAAE;QAC5B,IAAI,MAAM,CAAC,UAAU,IAAI,qBAAa,EAAE;YACtC,IAAI,IAAI,qBAAa,CAAC,MAAM,CAAC,UAA8B,CAAC,CAAC;YAC7D,IAAI,MAAM,CAAC,UAAU,KAAK,OAAO,EAAE;gBACjC,8CAA8C;gBAC9C,UAAU,GAAG,IAAI,CAAC;aACnB;SACF;aAAM;YACL,IAAI,sCAA8B,CAAC;YACnC,0CAA0C;YAC1C,IAAI,CAAC,CAAC,IAAI,2CAAiC,CAAC,EAAE;gBAC5C,UAAU,GAAG,KAAK,CAAC;aACpB;SACF;KACF;IACD,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;QACvD,8CAA8C;QAC9C,UAAU,GAAG,IAAI,CAAC;KACnB;IACD,IAAI,UAAU,EAAE;QACd,IAAI,yCAAgC,CAAC;KACtC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AA5BD,sDA4BC"}