{"program": {"fileNames": ["../../../node_modules/typescript/lib/lib.es5.d.ts", "../../../node_modules/typescript/lib/lib.es2015.d.ts", "../../../node_modules/typescript/lib/lib.es2016.d.ts", "../../../node_modules/typescript/lib/lib.es2017.d.ts", "../../../node_modules/typescript/lib/lib.es2018.d.ts", "../../../node_modules/typescript/lib/lib.es2019.d.ts", "../../../node_modules/typescript/lib/lib.es2020.d.ts", "../../../node_modules/typescript/lib/lib.es2021.d.ts", "../../../node_modules/typescript/lib/lib.es2022.d.ts", "../../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2022.array.d.ts", "../../../node_modules/typescript/lib/lib.es2022.error.d.ts", "../../../node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2022.object.d.ts", "../../../node_modules/typescript/lib/lib.es2022.sharedmemory.d.ts", "../../../node_modules/typescript/lib/lib.es2022.string.d.ts", "../../../node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../../node_modules/typescript/lib/lib.decorators.d.ts", "../../../node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../src/ClassHelpers.ts", "../src/ClassMap.ts", "../src/Collection.ts", "../src/Configuration.ts", "../src/Counter.ts", "../src/Dictionary.ts", "../src/GeoSpatial.ts", "../src/JSONCacheMap.ts", "../src/List.ts", "../src/Listeners.ts", "../src/Logger.ts", "../src/Object.ts", "../src/ObjectListeners.ts", "../src/OrderedCollection.ts", "../src/ProgressRealmPromise.ts", "../src/PromiseHandle.ts", "../src/PropertyHelpers.ts", "../src/PropertyMap.ts", "../src/Realm.ts", "../src/RealmListeners.ts", "../src/Results.ts", "../src/Set.ts", "../src/TimeoutPromise.ts", "../src/TypeHelpers.ts", "../src/Types.ts", "../src/Unmanaged.ts", "../src/assert.ts", "../src/async-iterator-from-response.ts", "../src/bson.ts", "../src/debug.ts", "../src/decorators.ts", "../src/deprecated-global.ts", "../src/errors.ts", "../src/flags.ts", "../src/index.ts", "../src/indirect.ts", "../src/namespace.ts", "../src/platform.ts", "../src/ranges.ts", "../src/runtime.d.ts", "../src/safeGlobalThis.ts", "../src/schema.ts", "../src/symbols.ts", "../src/binding/NativeBigInt.ts", "../src/binding/PolyfilledBigInt.ts", "../src/binding/index.ts", "../src/binding/patch.ts", "../src/binding/utils.ts", "../src/binding/wrapper.generated.ts", "../src/collection-accessors/Dictionary.ts", "../src/collection-accessors/List.ts", "../src/collection-accessors/OrderedCollection.ts", "../src/collection-accessors/Results.ts", "../src/collection-accessors/Set.ts", "../src/platform/binding.ts", "../src/platform/file-system.ts", "../src/platform/garbage-collection.ts", "../src/platform/network.ts", "../src/property-accessors/Array.ts", "../src/property-accessors/Dictionary.ts", "../src/property-accessors/Int.ts", "../src/property-accessors/Mixed.ts", "../src/property-accessors/Object.ts", "../src/property-accessors/Set.ts", "../src/property-accessors/default.ts", "../src/property-accessors/types.ts", "../src/schema/from-binding.ts", "../src/schema/normalize.ts", "../src/schema/to-binding.ts", "../src/schema/types.ts", "../src/schema/validate.ts", "../src/type-helpers/Array.ts", "../src/type-helpers/Bool.ts", "../src/type-helpers/Data.ts", "../src/type-helpers/Date.ts", "../src/type-helpers/Decimal.ts", "../src/type-helpers/Double.ts", "../src/type-helpers/Float.ts", "../src/type-helpers/Int.ts", "../src/type-helpers/LinkingObjects.ts", "../src/type-helpers/Mixed.ts", "../src/type-helpers/Object.ts", "../src/type-helpers/ObjectId.ts", "../src/type-helpers/String.ts", "../src/type-helpers/Uuid.ts", "../src/type-helpers/array-buffer.ts", "../src/type-helpers/default.ts", "../src/type-helpers/null-passthrough.ts", "../src/type-helpers/types.ts", "../../../node_modules/bson/bson.d.ts", "../../../node_modules/@types/debug/index.d.ts", "../../fetch/dist/types/types.d.ts"], "fileInfos": [{"version": "6a6b471e7e43e15ef6f8fe617a22ce4ecb0e34efa6c3dfcfe7cebd392bcca9d2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "dc48272d7c333ccf58034c0026162576b7d50ea0e69c3b9292f803fc20720fd5", "impliedFormat": 1}, {"version": "27147504487dc1159369da4f4da8a26406364624fa9bc3db632f7d94a5bae2c3", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "5514e54f17d6d74ecefedc73c504eadffdeda79c7ea205cf9febead32d45c4bc", "impliedFormat": 1}, {"version": "f296963760430fb65b4e5d91f0ed770a91c6e77455bacf8fa23a1501654ede0e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5114a95689b63f96b957e00216bc04baf9e1a1782aa4d8ee7e5e9acbf768e301", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4443e68b35f3332f753eacc66a04ac1d2053b8b035a0e0ac1d455392b5e243b3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab22100fdd0d24cfc2cc59d0a00fc8cf449830d9c4030dc54390a46bd562e929", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f7bd636ae3a4623c503359ada74510c4005df5b36de7f23e1db8a5c543fd176b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0c20f4d2358eb679e4ae8a4432bdd96c857a2960fd6800b21ec4008ec59d60ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "36ae84ccc0633f7c0787bc6108386c8b773e95d3b052d9464a99cd9b8795fbec", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "82d0d8e269b9eeac02c3bd1c9e884e85d483fcb2cd168bccd6bc54df663da031", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b8deab98702588840be73d67f02412a2d45a417a3c097b2e96f7f3a42ac483d1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4738f2420687fd85629c9efb470793bb753709c2379e5f85bc1815d875ceadcd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "376d554d042fb409cb55b5cbaf0b2b4b7e669619493c5d18d5fa8bd67273f82a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9fc46429fbe091ac5ad2608c657201eb68b6f1b8341bd6d670047d32ed0a88fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "61c37c1de663cf4171e1192466e52c7a382afa58da01b1dc75058f032ddf0839", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c4138a3dd7cd6cf1f363ca0f905554e8d81b45844feea17786cdf1626cb8ea06", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6ff3e2452b055d8f0ec026511c6582b55d935675af67cdb67dd1dc671e8065df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "03de17b810f426a2f47396b0b99b53a82c1b60e9cba7a7edda47f9bb077882f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8184c6ddf48f0c98429326b428478ecc6143c27f79b79e85740f17e6feb090f1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "261c4d2cf86ac5a89ad3fb3fafed74cbb6f2f7c1d139b0540933df567d64a6ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6af1425e9973f4924fca986636ac19a0cf9909a7e0d9d3009c349e6244e957b6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "15a630d6817718a2ddd7088c4f83e4673fde19fa992d2eae2cf51132a302a5d3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b7e9f95a7387e3f66be0ed6db43600c49cec33a3900437ce2fd350d9b7cb16f2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "01e0ee7e1f661acedb08b51f8a9b7d7f959e9cdb6441360f06522cc3aea1bf2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac17a97f816d53d9dd79b0d235e1c0ed54a8cc6a0677e9a3d61efb480b2a3e4e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "bf14a426dbbf1022d11bd08d6b8e709a2e9d246f0c6c1032f3b2edb9a902adbe", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ec0104fee478075cb5171e5f4e3f23add8e02d845ae0165bfa3f1099241fa2aa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2b72d528b2e2fe3c57889ca7baef5e13a56c957b946906d03767c642f386bbc3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9cc66b0513ad41cb5f5372cca86ef83a0d37d1c1017580b7dace3ea5661836df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "709efdae0cb5df5f49376cde61daacc95cdd44ae4671da13a540da5088bf3f30", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "61ed9b6d07af959e745fb11f9593ecd743b279418cc8a99448ea3cd5f3b3eb22", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "038a2f66a34ee7a9c2fbc3584c8ab43dff2995f8c68e3f566f4c300d2175e31e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4fa6ed14e98aa80b91f61b9805c653ee82af3502dc21c9da5268d3857772ca05", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f5c92f2c27b06c1a41b88f6db8299205aee52c2a2943f7ed29bd585977f254e8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b7feb7967c6c6003e11f49efa8f5de989484e0a6ba2e5a6c41b55f8b8bd85dba", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b9ea5778ff8b50d7c04c9890170db34c26a5358cccba36844fe319f50a43a61a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "50d53ccd31f6667aff66e3d62adf948879a3a16f05d89882d1188084ee415bbc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "189c0703923150aa30673fa3de411346d727cc44a11c75d05d7cf9ef095daa22", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d97a56a8d7ac18f8de4de87b847bd6563486cf0a07a66364f5d6ba9c6946f5a6", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "06fc2ca079c219f9442a046e613bf6f83f3a03287d977490d1066f4da8d49a97", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "2b7fc2dd19c8455d1242eaa3e4b5f462ea6032f01d24d22b26f98ea90e7a0c27", "signature": "b1108aa1daec46eef07090b95dc9b99e1a9bc93c0239da13fd6dba753255ede7", "impliedFormat": 1}, {"version": "b35fbe8a1a8f9f0dbd7374ce8d8de648b17f817b18a70c5dbe18235de1d06d65", "signature": "a0d07d52172e1e74b560293252a9346b451328d4cd1ac38ecc0ec48c39b511dc", "impliedFormat": 1}, {"version": "c563c99a723aba8168809bf497f05b8b746aafd569d04c5e1192d2116f81e45a", "signature": "2cda3769d8643a1f3149635f68fae5b4d208aa67ccad8b19c8b61fb6d8ef7bb0", "impliedFormat": 1}, {"version": "d5c916e39a3a64d1cf0f55cf7c4369a23b9ff0977c6e44da5b84e37bddb3bf9e", "signature": "d795574e177073be77a7038ab80555d45be235cf0a477e662fdc92b2d2941158", "impliedFormat": 1}, {"version": "b19919da6cbc9f556041026628a0f52eb8b6f73429ab7b4f4cd8a96e69c287e3", "signature": "e6e81e2fbd9e54c6bf4461fb67c5b2a142bbcb89719788afea1c8adf51263159", "impliedFormat": 1}, {"version": "1934a942af538204fe80b14cdaa639f552429d0e30713c44f5dd335fb0d0e670", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "4412e25cdafdf7381fd31a2157bf57c44c56208c3cc99f6a6ac5d8895ed41b8f", "signature": "90474ee54856c704dea19f846cb3887eabce7c4f65429f12b3c3a6f32b65b514", "impliedFormat": 1}, {"version": "6f4a78b82905f545a595278df838ed7cbbd741b33c66f47044af640b342342dc", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "a8868c2feb155ee0e0ca1622d3eca1220c7fce88f2e0b51e3f1d22406cec2d0e", "signature": "72958c5871a862307856680e2776566dc0e4ca09e5784a45b17a234c0757d9c2", "impliedFormat": 1}, {"version": "b1a1b179f82eec634469152fed2cc20367257ff11b66fbad124cb105bc7e0ff9", "signature": "2aec2f4263e6557a9b22c495b64bdb8744f4e8c998ee6316eea9be89b79d4b74", "impliedFormat": 1}, {"version": "c13e2050cca58181f18224f54fe180a339cb2fa4d97932281c72f248a1412970", "signature": "8c3e212d2c5c6daf3235a62827f148f901811871d9837fdf177c443876e10785", "impliedFormat": 1}, {"version": "8f821cada0eda8971ca804d76f8db02eb439711d2f1269b96fbefc9e0b85547a", "signature": "03bbec33ded083185eb4a2748bacf86107064db2e1bf4f5c5771eeac052b4a54", "impliedFormat": 1}, {"version": "cb7f52f0e8a17ba634f5b2f6906d5d69d7edb907004732e0d8cebef26c9b0d3a", "signature": "394ec2f391833929de46bc7e95446c7d290d963d934f10fb9341b7a2f4c12059", "impliedFormat": 1}, {"version": "380e4a235dfb37fd19898b0b44b5c76e79daabbecaa68a2050c6f1be933b207a", "signature": "57fb853a9b21cb0a0c9eebe738525880a3cd855278e673e173ccbfa0d4d6a664", "impliedFormat": 1}, {"version": "dd8969aaf8be7ad739656f6fd9280660e742be321ad5d84e1f6449659f7cbcbf", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "09a349d008843d5fbc4dcd6fe648177103933300c886ce27b2b37fab798bf9e3", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "bd61abb898fb04f7d322186138c89e31baf34bf860bb4b957a7ec07159effc9d", "signature": "c7b7bfdc42bc69b0a73d69da57281bc22961d0743882846342ab4fa4e7f97e60", "impliedFormat": 1}, {"version": "716149b124c385938fda69771730dc6f3e3d5880efa736fcb3bb2222dc0f58fa", "signature": "6a0c8dcdd854ab39b3f42ee70127fd250313635f4f45c991668232cf4bf5d279", "impliedFormat": 1}, {"version": "2d567c5069b853d57fd3988a20946c956618e74e9f4916223ecc4f15aecc6892", "signature": "1fab57987a9fcfcd03ce1e7ec206c465f6ba671d8c03e6001903f4e07b865081", "impliedFormat": 1}, {"version": "1fbf7cf16003408b492de19a1a455701d87b909faf2d316c94712bff2878ae31", "signature": "f1009c1c5450ee5e509ed359747982348d5daae392c2318357652e77437f799d", "impliedFormat": 1}, {"version": "bec68a12e7b3f750549b6b5ef8726602b5f6abeb6cc922e61ba7d94becfc7afa", "signature": "0e0775d8fb51baa03c0ac872bc07059e53409afe35ced9e945695c8a9c04c739", "impliedFormat": 1}, {"version": "6fb26288b7632471cdf58cdd92c4dcb80bfe86877eef0c699e3240f2087d9667", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "87dc371b06c5dfea0e59fee73537d785ae40425a5421423369a3a2f976de55f8", "signature": "f1b9573d9bd9e0d17da9f36647b26f74529c6fe8c012fc1e4923cf1dd09578f4", "impliedFormat": 1}, {"version": "d066d122b7d0319fea3ab406cf454752cc3166a555d5b804edf2bea1925b29e0", "signature": "904b64697a9e3b952a32ce1400509b5f751768cc517f37c998e46209cf77395a", "impliedFormat": 1}, {"version": "7dd2b4c9d31916a437d518164058ab60cb61c0cf1b08e18d02a7bbf8a42e2f14", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "0f0122ba0cf4d433460edd2e072661bb0771a9e61c333e5158f8b98f93688b63", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "7f8adcde223939c2e7657dddd71c7b97d306d98f8d1fd3c6015d8a83be12a012", "signature": "6a8b47ed9f44cdfa0ad5b4a3f4f3cf6070b4f60594e7f8579eae4c651e0e28c7", "impliedFormat": 1}, {"version": "ff0cfe65ff79eb6f52660054e6dfa6f2a53c698978c17cb1916e3c760b92fea1", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "06c18c3a1d9783696a5f5b221cbbee07334e42ff59f04d76360802f1767c5ffe", "signature": "078fb0215397ef63f62e921922dcec8e099ce420d7080ab6a06b24125ec1980c", "impliedFormat": 1}, {"version": "2b3b4e5b024684c5bc3a811f2dcdd2a7285c5e97c0a0bc3bc102cc582b094a70", "signature": "566cbaf88651760b6bd96e878e1746ea3f4c934655ee73d8301549a90e0115d3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3b5cc6df29c2df51f88b8fde1939dca8194fbfba807b3ce678106b5b954cf327", "signature": "7e334ddb98056560979d85b8e970c3bb408b04696eef408631cfef51e8c1202b", "impliedFormat": 1}, {"version": "85d36e09c9f5c1e20bd1508bf4a23be2bb8f5a1b5ddfdcc5279adea5e4ccd0b5", "signature": "eb35b88c1e50c9c1f9953c298878790f186c205ca209a5a5703a5c3ecd05688e", "impliedFormat": 1}, {"version": "5f3330df5b391cd132b6e8f63299204ab7e711afe77051c18fac9a98faab1254", "signature": "b8dae6794dcf53e6a441f196e68b31d84958e60601a807cdb0c7687706105c1f", "impliedFormat": 1}, {"version": "34ef80c34e7a8866a6c489d8f4467f642344e04a455c4e56f2fabd703cae2e09", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "0c5f7241526206eae9c6299c56dc2bb25cdbfa10891e95f17278e697f6f9881c", "signature": "ef8b2604e760a651d1bfd084064ff98fb6bee7c1a0b7d850d2c7b04bd9f9012d", "impliedFormat": 1}, {"version": "8f81515c8822dc7196ced6aa99e4303bf447fd16a14e93317f9889468a6ae1b2", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "3d612092b9fe9f5bba5fde209344838924c541979fd595584a623a685dfe1ee5", "signature": "6286c829b0f216f139161e18bc51b779ad4e97c106d8e7069d1f340476ba6754", "impliedFormat": 1}, {"version": "aa59c8d3604e422b90fcf77d98e2c9fdc90e7e7aee3a5cb4b5a5c4ced5622de8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1d78c1533f0b32c77225c1cd22090446d4d71f8ea8210a519e2dd4f4932e3ab6", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "0fc41bba5d7023736b9f81b4779e76ef84236c9c1612c068e694a39b9a617aed", "signature": "c3842335656432f6d3bf18eb0d4a03ee2fbdfeae3698a5afe76a6bb43170fb99", "impliedFormat": 1}, {"version": "b734eacc2bdd99e3d6e6863457c0a54c4f6187e5f08bd414b3d967b4d66b8d12", "signature": "e9e1c22baac08dc8dc68efea2883959c345755d20aea13cc1ff9fad5d0f2a0df", "impliedFormat": 1}, {"version": "8f0f8a54b4a4c32374ecef88d39346f24c901b03f3de8b4f397b7093fb6c95cd", "signature": "63c20d1f912f76b64cbe729cfa165e6e7e25fde64b8a45a6a6bc031a7fab86af", "impliedFormat": 1}, {"version": "981b123490364792a75330b5c353055df6913874fb0a292b63c7902c3b9fbadc", "signature": "88029655877960eddc5a37db2520cfefc9182d298cb152d82588af119d29f22b", "impliedFormat": 1}, {"version": "84c405237d82eb5f93743021b216bbc70959d4be2d3326135e5da33317d1ba55", "signature": "f8ce55f68b80ba5149852a103328511b71b4e31e029cacf97bf37efdbf255278", "impliedFormat": 1}, {"version": "e4551d68ba24196e494d0ccd36181b1ef195c5a028c723d1ac3c281b93c9fea2", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "8664e27d170a166fef3bfb569e866c836ab9774d68bd0b835c9f9c70f593b5ac", "signature": "97b21dfbcc7d6e6dff785ae724002d1686b2920b0962e3b37dfe0c2e3ecd2ed3", "impliedFormat": 1}, {"version": "73b0253b460f6e10d25a156fc394a8c0137a9a7d2dc1b618d1468a90012e0797", "signature": "ed4bc613da6295718177249ae67c40f702442177afb652d5b06b82b4bc4011aa", "impliedFormat": 1}, {"version": "2157132660d11dcf0b1b9785e4df4a5b60a500b52fe0d925072655e61beb9621", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "b265a298d5244469e08a74d1b58d1a572ebd0199a5f46d238edcd8d04b5e296c", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "550c5378e91b8c77dffd2db202a405199463a153a3deabeeeb8a6c4e0b40d266", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "4d36585ee42eb98e378216c46773d2ab675a1498c45b76aea581c443e2a946c3", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "c4197c3965b57184dd8e746ec2adad50fb8a9af133d4fb1e4d5cce490c9917c7", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "97bee418eb86423f9600d1265ac6f18d977bbe99b43c6ef725262ef1560c4c2b", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "f57c58db73964014930a03f96e17d00ac452af2e4b1e0ff5e926accc49933df0", "signature": "33e28f2bdd77e5880fef9d3ee48d90157ad78045ed0628a3fc536b295da78503", "impliedFormat": 1}, {"version": "8f89f256ef5eb87d0c1fdded9e7a2d3d69778c07deac3831b3f2223850e9ab01", "signature": "2b24d5f0ff3ae20d455d49145681de971a76eb736f3740369e80be0fd6b91fcc", "impliedFormat": 1}, {"version": "17e6ea500d283b684b79d727e08fdb2b29c11c65875c5311d2a366f22b745b1f", "signature": "504eb15c000f44f763028cf6e7bff3531b9143ae1c96a25c8e3cd170beabff3e", "impliedFormat": 1}, {"version": "51fc2d84839eb93cc0548826e31c2b91f98a9d9e5c57f2c7c61b2c868240641a", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "4ff0dc457beced5dd2edeb2953e98c405a8c68e1448d7bfb27f20824d840d403", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "0c0710242f63f251dd24d17ef597431d9326a14fecf48a296fd1166c960461a4", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "0d83ca9f1266fc8286f9b8d570374cfcad636e87d60cb3da38a41e931f81f51b", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "12dda173e9ad8495e4bcb1f6f90eff7e1cf74b4380999a3c0ea13094ea6ca0cd", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "c0a8ff98805a13abf807b701685d6e05c1b1bbe318708118516c2d2cc316a4a2", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "3f0a5c825b696d85134e83c1018620a9a4b53d83f03aba0662fec96649582649", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "adb9c2a483b058487d408e6b04c287cfd83892ff0d7f6a73ea5e5092572daf6c", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "828d9d93ded9c34c62ee42c27e5742a7cd80b49e1b48f827f931d97636dee09e", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "80773de11213ece488ce9cc8974232ff89bfaff5069f5330d45396e62c04f8b0", "signature": "f9a695f7f4df9d00bdd0104bd46acd499c521be7d1ef238d53b1afd1de7e352b", "impliedFormat": 1}, {"version": "b9855fc9f9f9e5bc8aed54ef59732b3da4c4cefccce07364eed4f001ce037614", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "485777506d5051d959d5d0ea2c69b34abf0ecc534d9fe11d99ecc54f4b55d97e", "signature": "5fd7c0c5fdf542758a6e0828b380f4863ff932fb3a69134412b6ff0ee7f146f1", "impliedFormat": 1}, {"version": "de0595f5ad8f14f89826117092046e05098ff430b577a920475108d2ce4f67ce", "signature": "f03e047f404817d6534e09019ae0b984b355d3af08cd3d96aaeed08a75b3edf0", "impliedFormat": 1}, {"version": "fd5f8a84a75d220f055a9f5fd30622aa75e145a54b8c81538888f52f01905b33", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "74b41858ac4a0d8fdfe388aa40a9bf681f3ca80471a49532ed823b6d9ec8306b", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "8f9df29e2533a39e1ac8e6e42a94e9d8a8a48a4123ec6ab146483f4d5214218c", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "d74b51528b39805f9431394ebc4f5d48d04023605fdaba1ef78e7ad61bb26b40", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "0bf02c66514a49856e48ca51f4a08733d786afc7ed3d0e638dcea225a52cdf0f", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "e6bf62ef894670600d54221bb558afe2776b301fc3c6dbf95d57be9efb877e93", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "34313565df8ca359beda3edc0e86dc866f5f4dee39db5aaa9f42abfed905ea2d", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "aca6771f2cd741caefd9df32a38a0855decd96358ef9ea9826a308fd97a7d30c", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "25870cd6ca75f56fc7539ce773459212f4255776ca4a4a413dc9f962ecd451b8", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "6350b58d9720072f8025d25aec715e0dbf19e314584f5061b95f6a5656e6ed51", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "bd9162127a2d81dcd32120551ecc583256e12758f9575ca530c2765f74f46dd5", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "d95ec0929fa66817b34fa8a3e36a0bb4dfe51c687663534c18d933e7a155285b", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "699d9f156a366cce6cd47e6d6dc59cb2a6f9b6cab1dbddfd59c4ceeac2d03ec4", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "2b9de2e7386cfc6bef3c2d002f3ec1c59f9c5c258ed1f560bf62d9d0e5667a01", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "13c2be201feb900447ca1dc035305c6fcaecdf81b6ec5574ee3c8cb7552903d3", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "7c4c9f0ab011d40a36a0730c57b59dafad0290129e737d211d81c3cf3e5effb0", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "ca0d1835df10786af35f73f2851468acf35c0000d42ba849f9b43edb1835907d", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "2dfacf18406ddf791ba8edcd7df8fe73bd5321c2187eb3d5dd5e7c76801d5402", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", "impliedFormat": 1}, {"version": "78058b4bb5b052718f6794d96b9207df1e4fbc361c36bf97d46ac628fc19a932", "impliedFormat": 1}, {"version": "3eb11dbf3489064a47a2e1cf9d261b1f100ef0b3b50ffca6c44dd99d6dd81ac1", "impliedFormat": 1}, {"version": "40d5ecd301b1393d29cb218bc2004d79d839c900778b2ca4c7198b2f54ee8ab6", "impliedFormat": 1}], "root": [[56, 144]], "options": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "esModuleInterop": true, "module": 100, "outDir": "./public-types", "rootDir": "../src", "skipLibCheck": true, "sourceMap": true, "strict": true, "stripInternal": true, "target": 9}, "fileIdsList": [[95], [67, 73, 97, 98, 101], [56, 67, 73, 82, 97, 98, 101], [61, 64, 65, 69, 76, 77, 79, 82, 88, 91, 101, 105], [67, 74, 82, 88, 97], [67, 74, 82, 88, 101], [58, 63, 67, 74, 79, 82, 88, 91, 97, 101, 105, 108], [101], [67, 97, 98], [58, 69, 74, 79, 82, 88, 91, 97, 101, 106], [74, 82, 101], [56, 58, 63, 68, 74, 76, 79, 81, 82, 84, 88, 89, 91, 97, 98, 101, 108], [56, 65, 67, 73, 98, 101], [56, 58, 63, 67, 74, 76, 79, 82, 88, 91, 94, 97, 98, 101, 106, 108, 109, 136], [59, 71, 74, 78, 89, 91, 101], [82], [79, 101, 114, 115, 116, 117, 118, 119, 120, 121], [72, 82, 97, 101, 121], [56, 57, 59, 64, 66, 67, 70, 75, 76, 79, 81, 82, 85, 88, 89, 91, 92, 93, 97, 98, 101, 108, 141], [74, 97], [58, 69, 74, 79, 81, 82, 88, 91, 101, 108], [58, 69, 74, 79, 82, 88, 91, 101, 109], [71, 88], [82, 101, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 144], [60, 61, 64, 76, 77, 84], [58, 60, 61, 64, 67, 74, 77], [74, 88, 97, 101], [147], [82, 102, 104], [82, 102, 104, 145], [102, 104], [102, 103, 145], [145], [61, 74, 79, 82, 91, 101, 106], [64, 74, 79, 82, 91, 101, 105, 107], [69, 79, 101], [79, 91, 101, 105, 106, 107], [74, 79, 82, 101, 107], [146], [74, 89, 92, 96], [74, 87], [58, 61, 64, 67, 69, 74, 76, 77], [58, 59, 60, 61, 62, 64, 66, 67, 68, 69, 70, 71, 74, 75, 76, 77, 80, 81, 84, 86, 88, 89, 97], [110, 111, 112, 113], [99, 100, 102, 104], [64, 76, 79, 82, 88, 101, 106, 108, 121], [61, 79, 82, 88, 101, 105, 121], [60, 120, 121], [61, 64, 82, 101, 105, 106, 120, 121], [82, 101, 120, 121], [77, 79, 82, 101, 109, 121], [82, 101, 121], [56, 74, 79, 97, 101, 106], [122, 123, 124, 125, 126], [82, 101, 125], [82, 88, 89, 97], [82, 97, 101], [67, 74, 84], [59, 82, 88, 91, 97], [82, 91, 101, 144], [82, 142, 143, 144], [141, 142, 143, 144], [82, 101, 143, 144], [82, 84, 142, 143, 144], [60, 88, 101, 143, 144], [82, 101, 142, 144], [60, 62, 79, 82, 91, 98, 101, 105, 106, 141, 144], [67, 82, 98, 101, 143, 144], [82, 96, 101], [56, 67, 74, 97, 101], [67, 74, 97], [58, 97], [69], [68, 74, 76, 81, 97], [67], [58, 76, 97], [74], [59, 64, 66, 67, 70, 75, 76, 81, 92, 97], [69, 81], [58, 60, 61, 64, 67, 77], [74, 92], [97], [67, 84], [59, 97]], "referencedMap": [[145, 1], [56, 2], [57, 3], [58, 4], [59, 5], [60, 6], [61, 7], [62, 8], [63, 9], [64, 10], [66, 11], [67, 12], [68, 13], [69, 14], [70, 15], [71, 16], [72, 17], [73, 18], [74, 19], [75, 20], [76, 21], [77, 22], [78, 23], [79, 24], [80, 25], [81, 26], [82, 27], [83, 28], [99, 29], [100, 30], [101, 31], [102, 31], [104, 32], [84, 33], [105, 34], [106, 35], [107, 36], [108, 37], [109, 38], [85, 39], [87, 40], [90, 41], [91, 42], [92, 43], [93, 44], [110, 45], [113, 28], [114, 46], [115, 47], [116, 48], [117, 49], [118, 50], [119, 51], [120, 52], [121, 53], [97, 54], [122, 55], [123, 56], [124, 57], [125, 58], [126, 59], [127, 60], [128, 61], [129, 62], [130, 63], [131, 64], [132, 61], [133, 63], [134, 65], [135, 66], [136, 67], [137, 68], [138, 64], [139, 61], [140, 64], [141, 69], [142, 8], [144, 70]], "exportedModulesMap": [[145, 1], [59, 71], [61, 72], [64, 73], [67, 74], [68, 75], [69, 76], [70, 77], [74, 78], [75, 20], [76, 79], [77, 73], [80, 25], [81, 80], [99, 31], [100, 31], [101, 31], [104, 32], [84, 33], [87, 81], [90, 41], [92, 43], [113, 28], [97, 54], [123, 82], [125, 83], [126, 84]], "semanticDiagnosticsPerFile": [146, 145, 54, 55, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 23, 20, 21, 22, 24, 25, 26, 5, 27, 28, 29, 30, 6, 34, 31, 32, 33, 35, 7, 36, 41, 42, 37, 38, 39, 40, 8, 46, 43, 44, 45, 47, 9, 48, 49, 50, 53, 51, 52, 1, 147, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 99, 100, 101, 102, 103, 104, 84, 105, 106, 107, 108, 109, 85, 86, 87, 88, 89, 90, 91, 92, 93, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 94, 95, 96, 97, 122, 123, 124, 125, 126, 98, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144], "latestChangedDtsFile": "./public-types/type-helpers/types.d.ts"}, "version": "5.0.4"}