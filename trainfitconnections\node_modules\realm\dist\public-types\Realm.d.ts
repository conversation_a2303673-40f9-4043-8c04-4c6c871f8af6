import type { Unmanaged } from "./Unmanaged";
import { type AnyRealmObject, RealmObject } from "./Object";
import { type AnyResults, Results } from "./Results";
import { type CanonicalObjectSchema, type Constructor, type DefaultObject, type ObjectSchema } from "./schema";
import { type Configuration } from "./Configuration";
import { type LogCategory, type LogLevel, type LoggerCallback1, type LoggerCallback2 } from "./Logger";
import { type AnyList } from "./List";
import { ProgressRealmPromise } from "./ProgressRealmPromise";
import { UpdateMode } from "./Object";
import { type RealmListenerCallback } from "./RealmListeners";
export type RealmEventName = "change" | "schema" | "beforenotify";
/**
 * The Realm database.
 */
export declare class Realm {
    static defaultPath: string;
    private static internals;
    /**
     * Sets the log level.
     * @param level - The log level to be used by the logger. The default value is `info`.
     * @param category - The category to set the log level for. If omitted, the log level is set for all categories (`"Realm"`).
     * @note The log level can be changed during the lifetime of the application.
     * @since 12.0.0
     * @example
     * Realm.setLogLevel("all");
     */
    static setLogLevel(level: LogLevel, category?: LogCategory): void;
    /**
     * Sets the logger callback.
     * @param loggerCallback - The callback invoked by the logger. The default callback uses `console.log`, `console.warn` and `console.error`, depending on the level of the message.
     * @note The logger callback needs to be set up before opening the first Realm.
     * @since 12.0.0
     * @example
     * Realm.setLogger(({ category, level, message }) => {
     *   console.log(`[${category} - ${level}] ${message}`);
     * });
     */
    static setLogger(loggerCallback: LoggerCallback2): void;
    /**
     * Sets the logger callback.
     * @param loggerCallback - The callback invoked by the logger. The default callback uses `console.log`, `console.warn` and `console.error`, depending on the level of the message.
     * @note The logger callback needs to be set up before opening the first Realm.
     * @since 12.0.0
     * @deprecated Pass a callback taking a single object argument instead.
     * @example
     * Realm.setLogger((level, message) => {
     *   console.log(`[${level}] ${message}`);
     * });
     */
    static setLogger(loggerCallback: LoggerCallback1): void;
    /**
     * Closes all Realms, cancels all pending {@link Realm.open} calls, clears internal caches, resets the logger and collects garbage.
     * Call this method to free up the event loop and allow Node.js to perform a graceful exit.
     */
    static shutdown(): void;
    /**
     * Clears the state by closing and deleting any Realm in the default directory and logout all users.
     * NOTE: Not a part of the public API and it's primarily used from the library's tests.
     * @private
     */
    static clearTestState(): void;
    /**
     * Delete the Realm file for the given configuration.
     * @param config - The configuration for the Realm being deleted.
     * @throws An {@link Error} if anything in the provided {@link config} is invalid.
     */
    static deleteFile(config: Configuration): void;
    /**
     * Checks if the Realm already exists on disk.
     * @param path - The path for a Realm.
     * @throws An {@link Error} if anything in the provided {@link path} is invalid.
     * @returns `true` if the Realm exists on the device, `false` if not.
     */
    static exists(path: string): boolean;
    /**
     * Checks if the Realm already exists on disk.
     * @param config - The configuration of a Realm.
     * @throws An {@link Error} if anything in the provided {@link config} is invalid.
     * @returns `true` if the Realm exists on the device, `false` if not.
     */
    static exists(config: Configuration): boolean;
    /**
     * Open the default Realm asynchronously with a promise.
     * @returns A promise that will be resolved with the Realm instance when it's available.
     */
    static open(): ProgressRealmPromise;
    /**
     * Open a Realm asynchronously with a promise. If the Realm is synced, it will be fully
     * synchronized before it is available.
     * @param path - The path for the Realm.
     * @returns A promise that will be resolved with the Realm instance when it's available.
     */
    static open(path: string): ProgressRealmPromise;
    /**
     * Open a Realm asynchronously with a promise. If the Realm is synced, it will be fully
     * synchronized before it is available.
     * In the case of query-based sync, {@link Configuration.schema} is required. An exception will be
     * thrown if {@link Configuration.schema} is not defined.
     * @param config - The configuration for the Realm.
     * @returns A promise that will be resolved with the Realm instance when it's available.
     * @throws An {@link Error} if anything in the provided {@link config} is invalid.
     */
    static open(config: Configuration): ProgressRealmPromise;
    /**
     * Get the current schema version of the Realm at the given path.
     * @param path - The path to the file where the Realm database is stored.
     * @param encryptionKey - Required only when accessing encrypted Realms.
     * @throws An {@link Error} if passing an invalid or non-matching encryption key.
     * @returns Version of the schema as an integer, or `-1` if no Realm exists at {@link path}.
     * @since 0.11.0
     */
    static schemaVersion(path: string, encryptionKey?: ArrayBuffer | ArrayBufferView): number;
    /**
     * Creates a template object for a Realm model class where all optional fields are undefined
     * and all required fields have the default value for the given data type, either the value
     * set by the default property in the schema or the default value for the datatype if the schema
     * doesn't specify one, i.e. 0, false and "".
     * @param objectSchema - Schema describing the object that should be created.
     */
    static createTemplateObject<T extends Record<string, unknown>>(objectSchema: ObjectSchema): T;
    /**
     * Copy any Realm files  (i.e. `*.realm`) bundled with the application from the application
     * directory into the application's documents directory, so that they can be opened and used
     * by Realm. If the file already exists in the documents directory, it will not be
     * overwritten, so this can safely be called multiple times.
     *
     * This should be called before opening the Realm, in order to move the bundled Realm
     * files into a place where they can be written to.
     * @example
     * ```
     * // Given a bundled file, example.realm, this will copy example.realm (and any other .realm files)
     * // from the app bundle into the app's documents directory. If the file already exists, it will
     * // not be overwritten, so it is safe to call this every time the app starts.
     * Realm.copyBundledRealmFiles();
     *
     * const realm = await Realm.open({
     * // This will open example.realm from the documents directory, with the bundled data in.
     * path: "example.realm"
     * });
     * ```
     *
     * This is only implemented for React Native.
     * @throws an {@link Error} If an I/O error occurred or method is not implemented.
     */
    static copyBundledRealmFiles(): void;
    /**
     * TODO: Consider breaking this by ensuring a ".realm" suffix (coordinating with other SDK teams in the process)
     */
    private static normalizePath;
    /**
     * @note When the path is relative and the config contains a sync object, Core will replace any existing file extension
     * or add the ".realm" suffix.
     */
    private static determinePath;
    private static determineEncryptionKey;
    private static extractRealmSchemaExtras;
    private static determineSchemaMode;
    private static wrapMigration;
    private schemaExtras;
    private classes;
    private changeListeners;
    private beforeNotifyListeners;
    private schemaListeners;
    /**
     * Create a new {@link Realm} instance, at the default path.
     * @throws An {@link Error} when an incompatible synced Realm is opened.
     */
    constructor();
    /**
     * Create a new {@link Realm} instance at the provided {@link path}.
     * @param path - Required when first creating the Realm.
     * @throws An {@link Error} if the Realm cannot be opened at the provided {@link path}.
     * @throws An {@link Error} when an incompatible synced Realm is opened.
     */
    constructor(path: string);
    /**
     * Create a new {@link Realm} instance using the provided {@link config}. If a Realm does not yet exist
     * at {@link Configuration.path | config.path} (or {@link Realm.defaultPath} if not provided), then this constructor
     * will create it with the provided {@link Configuration.schema | config.schema} (which is _required_ in this case).
     * Otherwise, the instance will access the existing Realm from the file at that path.
     * In this case, {@link Configuration.schema | config.schema} is _optional_ or not have changed, unless
     * {@link Configuration.schemaVersion | config.schemaVersion} is incremented, in which case the Realm will be automatically
     * migrated to use the new schema.
     * In the case of query-based sync, {@link Configuration.schema | config.schema} is required. An exception will be
     * thrown if {@link Configuration.schema | config.schema} is not defined.
     * @param config - Required when first creating the Realm.
     * @throws An {@link Error} if anything in the provided {@link config} is invalid.
     * @throws An {@link Error} when an incompatible synced Realm is opened.
     */
    constructor(config: Configuration);
    /**
     * Indicates if this Realm contains any objects.
     * @returns `true` if empty, `false` otherwise.
     * @readonly
     * @since 1.10.0
     */
    get isEmpty(): boolean;
    /**
     * The path to the file where this Realm is stored.
     * @returns A string containing the path to the file where this Realm is stored.
     * @readonly
     * @since 0.12.0
     */
    get path(): string;
    /**
     * Indicates if this Realm was opened as read-only.
     * @returns `true` if this Realm is read-only, `false` otherwise.
     * @readonly
     * @since 0.12.0
     */
    get isReadOnly(): boolean;
    /**
     * Indicates if this Realm was opened in-memory.
     * @returns `true` if this Realm is in-memory, `false` otherwise.
     * @readonly
     */
    get isInMemory(): boolean;
    /**
     * A normalized representation of the schema provided in the {@link Configuration} when this Realm was constructed.
     * @returns An array of {@link CanonicalObjectSchema} describing all objects in this Realm.
     * @readonly
     * @since 0.12.0
     */
    get schema(): CanonicalObjectSchema[];
    /**
     * The current schema version of the Realm.
     * @returns The schema version of this Realm, as a `number`.
     * @readonly
     * @since 0.12.0
     */
    get schemaVersion(): number;
    /**
     * Indicates if this Realm is in a write transaction.
     * @returns `true` if in a write transaction, `false` otherwise.
     * @readonly
     * @since 1.10.3
     */
    get isInTransaction(): boolean;
    /**
     * Indicates if this Realm is in migration.
     * @returns `true` if in migration, `false` otherwise
     * @readonly
     * @since 12.3.0
     */
    get isInMigration(): boolean;
    /**
     * Indicates if this Realm has been closed.
     * @returns `true` if closed, `false` otherwise.
     * @readonly
     * @since 2.1.0
     */
    get isClosed(): boolean;
    /**
     * Closes this Realm so it may be re-opened with a newer schema version.
     * All objects and collections from this Realm are no longer valid after calling this method.
     * The method is idempotent.
     */
    close(): void;
    /**
     * Create a new {@link RealmObject} of the given type and with the specified properties. For objects marked asymmetric,
     * `undefined` is returned. The API for asymmetric objects is subject to changes in the future.
     * @param type - The type of Realm object to create.
     * @param values - Property values for all required properties without a
     * default value.
     * @param mode Optional update mode. The default is `UpdateMode.Never`.
     * @returns A {@link RealmObject} or `undefined` if the object is asymmetric.
     */
    create<T = DefaultObject>(type: string, values: Partial<T> | Partial<Unmanaged<T>>, mode?: UpdateMode.Never | UpdateMode.All | UpdateMode.Modified | boolean): RealmObject<T> & T;
    create<T extends AnyRealmObject>(type: Constructor<T>, values: Partial<T> | Partial<Unmanaged<T>>, mode?: UpdateMode.Never | UpdateMode.All | UpdateMode.Modified | boolean): T;
    /**
     * Deletes the provided Realm object, or each one inside the provided collection.
     * @param subject - The Realm object to delete, or a collection containing multiple Realm objects to delete.
     */
    delete(subject: AnyRealmObject | AnyRealmObject[] | AnyList | AnyResults | any): void;
    /**
     * Deletes a Realm model, including all of its objects.
     * If called outside a migration function, {@link schema} and {@link schemaVersion} are updated.
     * @param name - The model name.
     */
    deleteModel(name: string): void;
    /**
     * **WARNING:** This will delete **all** objects in the Realm!
     */
    deleteAll(): void;
    /**
     * Searches for a Realm object by its primary key.
     * @param type - The type of Realm object to search for.
     * @param primaryKey - The primary key value of the object to search for.
     * @throws An {@link Error} if type passed into this method is invalid, or if the object type did
     * not have a {@link primaryKey} specified in the schema, or if it was marked asymmetric.
     * @returns A {@link RealmObject} or `null` if no object is found.
     * @since 0.14.0
     */
    objectForPrimaryKey<T = DefaultObject>(type: string, primaryKey: T[keyof T]): (RealmObject<T> & T) | null;
    objectForPrimaryKey<T extends AnyRealmObject>(type: Constructor<T>, primaryKey: T[keyof T]): T | null;
    _objectForObjectKey<T extends RealmObject>(type: Constructor<T>, objectKey: string): T | undefined;
    /**
     * Returns all objects of the given {@link type} in the Realm.
     * @param type - The type of Realm objects to retrieve.
     * @throws An {@link Error} if type passed into this method is invalid or if the type is marked embedded or asymmetric.
     * @returns Results that will live-update as objects are created, modified, and destroyed.
     */
    objects<T = DefaultObject>(type: string): Results<RealmObject<T> & T>;
    objects<T extends AnyRealmObject = RealmObject & DefaultObject>(type: Constructor<T>): Results<T>;
    /**
     * Add a listener {@link callback} for the specified {@link eventName}.
     * @param eventName - The name of event that should cause the callback to be called.
     * @param callback - Function to be called when a change event occurs.
     * Each callback will only be called once per event, regardless of the number of times
     * it was added.
     * @throws An {@link Error} if an invalid event {@link eventName} is supplied, if Realm is closed or if {@link callback} is not a function.
     */
    addListener(eventName: RealmEventName, callback: RealmListenerCallback): void;
    /**
     * Remove the listener {@link callback} for the specified event {@link eventName}.
     * @param eventName - The event name.
     * @param callback - Function that was previously added as a listener for this event through the {@link addListener} method.
     * @throws an {@link Error} If an invalid event {@link eventName} is supplied, if Realm is closed or if {@link callback} is not a function.
     */
    removeListener(eventName: RealmEventName, callback: RealmListenerCallback): void;
    /**
     * Remove all event listeners (restricted to the event {@link eventName}, if provided).
     * @param eventName - The name of the event whose listeners should be removed.
     * @throws An {@link Error} when invalid event {@link eventName} is supplied.
     */
    removeAllListeners(eventName?: RealmEventName): void;
    /**
     * Synchronously call the provided {@link callback} inside a write transaction. If an exception happens inside a transaction,
     * you’ll lose the changes in that transaction, but the Realm itself won’t be affected (or corrupted).
     * More precisely, {@link beginTransaction} and {@link commitTransaction} will be called
     * automatically. If any exception is thrown during the transaction {@link cancelTransaction} will
     * be called instead of {@link commitTransaction} and the exception will be re-thrown to the caller of {@link write}.
     *
     * Nested transactions (calling {@link write} within {@link write}) is not possible.
     * @param callback - Function to be called inside a write transaction.
     * @returns Returned value from the callback.
     */
    write<T>(callback: () => T): T;
    /**
     * Initiate a write transaction.
     *
     * When doing a transaction, it is highly recommended to do error handling.
     * If you don't handle errors, your data might become inconsistent. Error handling
     * will often involve canceling the transaction.
     * @throws An {@link Error} if already in write transaction
     * @see {@link cancelTransaction}
     * @see {@link commitTransaction}
     * @example
     * realm.beginTransaction();
     * try {
     *   realm.create('Person', { name: 'Arthur Dent',  origin: 'Earth' });
     *   realm.create('Person', { name: 'Ford Prefect', origin: 'Betelgeuse Five' });
     *   realm.commitTransaction();
     * } catch (e) {
     *   realm.cancelTransaction();
     *   throw e;
     * }
     */
    beginTransaction(): void;
    /**
     * Commit a write transaction.
     * @see {@link beginTransaction}
     */
    commitTransaction(): void;
    /**
     * Cancel a write transaction.
     * @see {@link beginTransaction}
     */
    cancelTransaction(): void;
    /**
     * Replaces all string columns in this Realm with a string enumeration column and compacts the
     * database file.
     *
     * Cannot be called from a write transaction.
     *
     * Compaction will not occur if other {@link Realm} instances exist.
     *
     * While compaction is in progress, attempts by other threads or processes to open the database will
     * wait.
     *
     * Be warned that resource requirements for compaction is proportional to the amount of live data in
     * the database. Compaction works by writing the database contents to a temporary database file and
     * then replacing the database with the temporary one.
     * @returns `true` if compaction succeeds, `false` if not.
     */
    compact(): boolean;
    /**
     * Writes a compacted copy of the Realm with the given configuration.
     *
     * The destination file cannot already exist.
     * All conversions between synced and non-synced Realms are supported, and will be
     * performed according to the {@link config} parameter, which describes the desired output.
     *
     * Note that if this method is called from within a write transaction, the current data is written,
     * not the data from the point when the previous write transaction was committed.
     * @param config - Realm configuration that describes the output realm.
     */
    writeCopyTo(config: Configuration): void;
}
import * as ns from "./namespace";
import RealmItself = Realm;
export declare namespace Realm {
    export import Realm = RealmItself;
    export import flags = ns.flags;
    export import Object = ns.RealmObject;
    export import BSON = ns.BSON;
    export import Types = ns.Types;
    export import index = ns.index;
    export import mapTo = ns.mapTo;
    export import kmToRadians = ns.kmToRadians;
    export import miToRadians = ns.miToRadians;
    export import AnyCollection = ns.AnyCollection;
    export import AnyDictionary = ns.AnyDictionary;
    export import AnyList = ns.AnyList;
    export import AnyRealmObject = ns.AnyRealmObject;
    export import AnyResults = ns.AnyResults;
    export import AnySet = ns.AnySet;
    export import AssertionError = ns.AssertionError;
    export import BaseObjectSchema = ns.BaseObjectSchema;
    export import CanonicalGeoPoint = ns.CanonicalGeoPoint;
    export import CanonicalGeoPolygon = ns.CanonicalGeoPolygon;
    export import CanonicalObjectSchema = ns.CanonicalObjectSchema;
    export import CanonicalPropertiesTypes = ns.CanonicalPropertiesTypes;
    export import CanonicalPropertySchema = ns.CanonicalPropertySchema;
    export import Collection = ns.Collection;
    export import CollectionChangeCallback = ns.CollectionChangeCallback;
    export import CollectionChangeSet = ns.CollectionChangeSet;
    export import CollectionPropertyTypeName = ns.CollectionPropertyTypeName;
    export import Configuration = ns.Configuration;
    export import Counter = ns.Counter;
    export import Dictionary = ns.Dictionary;
    export import DictionaryChangeCallback = ns.DictionaryChangeCallback;
    export import DictionaryChangeSet = ns.DictionaryChangeSet;
    export import GeoBox = ns.GeoBox;
    export import GeoCircle = ns.GeoCircle;
    export import GeoPoint = ns.GeoPoint;
    export import GeoPolygon = ns.GeoPolygon;
    export import GeoPosition = ns.GeoPosition;
    export import IndexDecorator = ns.IndexDecorator;
    export import IndexedType = ns.IndexedType;
    export import List = ns.List;
    export import LogCategory = ns.LogCategory;
    export import LogEntry = ns.LogEntry;
    export import LogLevel = ns.LogLevel;
    export import Logger = ns.Logger;
    export import LoggerCallback = ns.LoggerCallback;
    export import LoggerCallback1 = ns.LoggerCallback1;
    export import LoggerCallback2 = ns.LoggerCallback2;
    export import MapToDecorator = ns.MapToDecorator;
    export import MigrationCallback = ns.MigrationCallback;
    export import MigrationOptions = ns.MigrationOptions;
    export import Mixed = ns.Types.Mixed;
    export import NumericLogLevel = ns.NumericLogLevel;
    export import ObjectChangeCallback = ns.ObjectChangeCallback;
    export import ObjectChangeSet = ns.ObjectChangeSet;
    export import ObjectSchema = ns.ObjectSchema;
    export import ObjectType = ns.ObjectType;
    export import OrderedCollection = ns.OrderedCollection;
    export import PresentationPropertyTypeName = ns.PresentationPropertyTypeName;
    export import PrimaryKey = ns.PrimaryKey;
    export import PrimitivePropertyTypeName = ns.PrimitivePropertyTypeName;
    export import ProgressRealmPromise = ns.ProgressRealmPromise;
    export import PropertiesTypes = ns.PropertiesTypes;
    export import PropertySchema = ns.PropertySchema;
    export import PropertySchemaCommon = ns.PropertySchemaCommon;
    export import PropertySchemaParseError = ns.PropertySchemaParseError;
    export import PropertySchemaShorthand = ns.PropertySchemaShorthand;
    export import PropertySchemaStrict = ns.PropertySchemaStrict;
    export import PropertyTypeName = ns.PropertyTypeName;
    export import RealmEvent = ns.RealmEvent;
    export import RealmEventName = ns.RealmEventName;
    export import RealmListenerCallback = ns.RealmListenerCallback;
    export import RealmObjectConstructor = ns.RealmObjectConstructor;
    export import RelationshipPropertyTypeName = ns.RelationshipPropertyTypeName;
    export import Results = ns.Results;
    export import SchemaParseError = ns.SchemaParseError;
    export import Set = ns.RealmSet;
    export import ShorthandPrimitivePropertyTypeName = ns.ShorthandPrimitivePropertyTypeName;
    export import SortDescriptor = ns.SortDescriptor;
    export import TypeAssertionError = ns.TypeAssertionError;
    export import Unmanaged = ns.Unmanaged;
    export import UpdateMode = ns.UpdateMode;
    export import UserTypeName = ns.UserTypeName;
    /** @deprecated Will be removed in v13.0.0. Please use {@link ns.CanonicalPropertySchema | CanonicalPropertySchema} */
    export import CanonicalObjectSchemaProperty = ns.CanonicalPropertySchema;
    /** @deprecated Will be removed in v13.0.0. Please use {@link ns.PropertySchema | PropertySchema} */
    export import ObjectSchemaProperty = ns.PropertySchema;
    /** @deprecated Will be removed in v13.0.0. Please use {@link ns.RealmObjectConstructor | RealmObjectConstructor} */
    export import ObjectClass = ns.RealmObjectConstructor;
    /** @deprecated Will be removed in v13.0.0. Please use {@link ns.PropertyTypeName | PropertyTypeName} */
    export import PropertyType = ns.PropertyTypeName;
}
//# sourceMappingURL=Realm.d.ts.map