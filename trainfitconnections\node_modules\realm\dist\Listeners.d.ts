/** @internal */
export type CallbackAdder<CallbackType, TokenType, Args extends unknown[] = []> = (callback: CallbackType, ...args: Args) => TokenType;
/** @internal */
export type CallbackRemover<TokenType> = (token: TokenType) => void;
/** @internal */
export type ListenersOptions<CallbackType, TokenType, Args extends unknown[]> = {
    add: CallbackAdder<CallbackType, TokenType, Args>;
    remove: CallbackRemover<TokenType>;
};
/** @internal */
export declare class Listeners<CallbackType, TokenType, Args extends unknown[] = []> {
    private readonly options;
    constructor(options: ListenersOptions<CallbackType, TokenType, Args>);
    /**
     * Mapping of registered listener callbacks onto the their token in the bindings ObjectNotifier.
     */
    private listeners;
    add(callback: CallbackType, ...args: Args): void;
    remove(callback: CallbackType): void;
    removeAll(): void;
}
