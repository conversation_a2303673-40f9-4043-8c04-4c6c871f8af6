"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createNotification = createNotification;
exports.getNotificationsForUser = getNotificationsForUser;
exports.markNotificationAsRead = markNotificationAsRead;
exports.markAllNotificationsAsRead = markAllNotificationsAsRead;
exports.deleteNotification = deleteNotification;
exports.getUnreadNotificationCount = getUnreadNotificationCount;
exports.createReminder = createReminder;
exports.getRemindersForUser = getRemindersForUser;
exports.updateReminder = updateReminder;
exports.deleteReminder = deleteReminder;
exports.getDueReminders = getDueReminders;
exports.processReminder = processReminder;
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const dbName = 'trainfitconnections';
// CREATE a notification
async function createNotification(notificationData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const notificationId = new mongodb_2.ObjectId().toString();
    const notification = {
        ...notificationData,
        id: notificationId,
        isRead: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('notifications').insertOne(notification);
    return { ...notification, _id: result.insertedId };
}
// READ notifications for a user
async function getNotificationsForUser(userId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own notifications
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own notifications');
    }
    const query = {
        userId,
        $or: [
            { expiresAt: { $exists: false } },
            { expiresAt: { $gt: new Date() } }
        ]
    };
    // Apply filters
    if (filters?.isRead !== undefined) {
        query.isRead = filters.isRead;
    }
    if (filters?.type) {
        query.type = filters.type;
    }
    if (filters?.priority) {
        query.priority = filters.priority;
    }
    const limit = filters?.limit || 50;
    const offset = filters?.offset || 0;
    return db.collection('notifications')
        .find(query)
        .sort({ createdAt: -1 })
        .skip(offset)
        .limit(limit)
        .toArray();
}
// Mark notification as read
async function markNotificationAsRead(notificationId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const notification = await db.collection('notifications').findOne({ id: notificationId });
    if (!notification) {
        throw new Error('Notification not found');
    }
    // Only the recipient can mark notification as read
    if (notification.userId !== requestingUserId) {
        throw new Error('Access denied: You can only mark your own notifications as read');
    }
    const result = await db.collection('notifications').updateOne({ id: notificationId }, {
        $set: {
            isRead: true,
            readAt: new Date(),
            updatedAt: new Date(),
        }
    });
    return result;
}
// Mark all notifications as read for a user
async function markAllNotificationsAsRead(userId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only mark their own notifications as read
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only mark your own notifications as read');
    }
    const result = await db.collection('notifications').updateMany({
        userId,
        isRead: false
    }, {
        $set: {
            isRead: true,
            readAt: new Date(),
            updatedAt: new Date(),
        }
    });
    return result;
}
// DELETE a notification
async function deleteNotification(notificationId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const notification = await db.collection('notifications').findOne({ id: notificationId });
    if (!notification) {
        throw new Error('Notification not found');
    }
    // Only the recipient can delete notification
    if (notification.userId !== requestingUserId) {
        throw new Error('Access denied: You can only delete your own notifications');
    }
    const result = await db.collection('notifications').deleteOne({ id: notificationId });
    return result;
}
// Get unread notification count
async function getUnreadNotificationCount(userId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only check their own unread count
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only check your own unread notifications');
    }
    const count = await db.collection('notifications').countDocuments({
        userId,
        isRead: false,
        $or: [
            { expiresAt: { $exists: false } },
            { expiresAt: { $gt: new Date() } }
        ]
    });
    return count;
}
// CREATE a reminder
async function createReminder(reminderData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Verify user can create this reminder (must be for themselves)
    if (reminderData.userId !== requestingUserId) {
        throw new Error('Access denied: You can only create reminders for yourself');
    }
    const reminderId = new mongodb_2.ObjectId().toString();
    // Calculate next trigger time
    const nextTrigger = calculateNextTrigger(reminderData.reminderTime, reminderData.recurrencePattern);
    const reminder = {
        ...reminderData,
        id: reminderId,
        nextTrigger,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('reminders').insertOne(reminder);
    return { ...reminder, _id: result.insertedId };
}
// READ reminders for a user
async function getRemindersForUser(userId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own reminders
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own reminders');
    }
    const query = { userId };
    // Apply filters
    if (filters?.type) {
        query.type = filters.type;
    }
    if (filters?.isActive !== undefined) {
        query.isActive = filters.isActive;
    }
    if (filters?.upcoming) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        query.nextTrigger = { $lte: tomorrow };
    }
    return db.collection('reminders').find(query).sort({ nextTrigger: 1 }).toArray();
}
// UPDATE a reminder
async function updateReminder(reminderId, updates, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const existingReminder = await db.collection('reminders').findOne({ id: reminderId });
    if (!existingReminder) {
        throw new Error('Reminder not found');
    }
    // Only the owner can update the reminder
    if (existingReminder.userId !== requestingUserId) {
        throw new Error('Access denied: You can only update your own reminders');
    }
    // Remove fields that shouldn't be updated directly
    const { _id, id, userId, createdAt, ...safeUpdates } = updates;
    // Recalculate next trigger if reminder time or recurrence changed
    let nextTrigger = existingReminder.nextTrigger;
    if (safeUpdates.reminderTime || safeUpdates.recurrencePattern) {
        const newReminderTime = safeUpdates.reminderTime || existingReminder.reminderTime;
        const newRecurrencePattern = safeUpdates.recurrencePattern || existingReminder.recurrencePattern;
        nextTrigger = calculateNextTrigger(newReminderTime, newRecurrencePattern);
    }
    const updateData = {
        ...safeUpdates,
        nextTrigger,
        updatedAt: new Date(),
    };
    const result = await db.collection('reminders').updateOne({ id: reminderId }, { $set: updateData });
    return result;
}
// DELETE a reminder
async function deleteReminder(reminderId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const existingReminder = await db.collection('reminders').findOne({ id: reminderId });
    if (!existingReminder) {
        throw new Error('Reminder not found');
    }
    // Only the owner can delete the reminder
    if (existingReminder.userId !== requestingUserId) {
        throw new Error('Access denied: You can only delete your own reminders');
    }
    const result = await db.collection('reminders').deleteOne({ id: reminderId });
    return result;
}
// Helper function to calculate next trigger time
function calculateNextTrigger(reminderTime, recurrencePattern) {
    const now = new Date();
    let nextTrigger = new Date(reminderTime);
    // If it's a one-time reminder and the time has passed, return the original time
    if (!recurrencePattern) {
        return nextTrigger;
    }
    // For recurring reminders, find the next occurrence
    while (nextTrigger <= now) {
        switch (recurrencePattern.frequency) {
            case 'daily':
                nextTrigger.setDate(nextTrigger.getDate() + recurrencePattern.interval);
                break;
            case 'weekly':
                nextTrigger.setDate(nextTrigger.getDate() + (7 * recurrencePattern.interval));
                break;
            case 'monthly':
                nextTrigger.setMonth(nextTrigger.getMonth() + recurrencePattern.interval);
                break;
        }
        // Check if we've passed the end date
        if (recurrencePattern.endDate && nextTrigger > recurrencePattern.endDate) {
            break;
        }
    }
    return nextTrigger;
}
// Get due reminders (for background processing)
async function getDueReminders() {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const now = new Date();
    return db.collection('reminders').find({
        isActive: true,
        nextTrigger: { $lte: now }
    }).toArray();
}
// Process a reminder (mark as triggered and schedule next)
async function processReminder(reminderId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const reminder = await db.collection('reminders').findOne({ id: reminderId });
    if (!reminder) {
        throw new Error('Reminder not found');
    }
    const now = new Date();
    let updateData = {
        lastTriggered: now,
        updatedAt: now,
    };
    // If it's recurring, calculate next trigger
    if (reminder.isRecurring && reminder.recurrencePattern) {
        const nextTrigger = calculateNextTrigger(reminder.reminderTime, reminder.recurrencePattern);
        // If next trigger is past end date, deactivate reminder
        if (reminder.recurrencePattern.endDate && nextTrigger > reminder.recurrencePattern.endDate) {
            updateData.isActive = false;
            updateData.nextTrigger = null;
        }
        else {
            updateData.nextTrigger = nextTrigger;
        }
    }
    else {
        // One-time reminder, deactivate it
        updateData.isActive = false;
        updateData.nextTrigger = null;
    }
    const result = await db.collection('reminders').updateOne({ id: reminderId }, { $set: updateData });
    return result;
}
//# sourceMappingURL=notifications-crud.js.map