import { serve } from '@hono/node-server';
import { app } from './hono';

// For Vercel deployment
export default app;

// For local development
if (process.env.NODE_ENV !== 'production') {
  const port = process.env.PORT || 3000;

  console.log(`Starting server on port ${port}...`);

  serve({
    fetch: app.fetch,
    port: Number(port),
  });

  console.log(`Server running on http://localhost:${port}`);
}
