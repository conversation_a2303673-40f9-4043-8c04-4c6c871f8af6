import { ObjectId } from 'mongodb';
export interface Payment {
    _id?: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    sessionId?: string;
    amount: number;
    currency: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'cancelled';
    paymentMethod: 'credit_card' | 'debit_card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer' | 'cash';
    paymentIntentId?: string;
    transactionId?: string;
    description: string;
    metadata?: {
        sessionDate?: string;
        sessionType?: string;
        customRateId?: string;
    };
    fees?: {
        platformFee: number;
        processingFee: number;
        trainerEarnings: number;
    };
    refundAmount?: number;
    refundReason?: string;
    paidAt?: Date;
    refundedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface Revenue {
    _id?: ObjectId;
    id: string;
    trainerId: string;
    period: string;
    periodType: 'daily' | 'weekly' | 'monthly' | 'yearly';
    totalEarnings: number;
    totalSessions: number;
    totalClients: number;
    averageSessionRate: number;
    platformFees: number;
    processingFees: number;
    netEarnings: number;
    paymentBreakdown: {
        credit_card: number;
        paypal: number;
        cash: number;
        other: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare function createPayment(paymentData: Omit<Payment, '_id' | 'id' | 'createdAt' | 'updatedAt'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    sessionId?: string;
    amount: number;
    currency: string;
    status: "pending" | "processing" | "completed" | "failed" | "refunded" | "cancelled";
    paymentMethod: "credit_card" | "debit_card" | "paypal" | "apple_pay" | "google_pay" | "bank_transfer" | "cash";
    paymentIntentId?: string;
    transactionId?: string;
    description: string;
    metadata?: {
        sessionDate?: string;
        sessionType?: string;
        customRateId?: string;
    };
    fees?: {
        platformFee: number;
        processingFee: number;
        trainerEarnings: number;
    };
    refundAmount?: number;
    refundReason?: string;
    paidAt?: Date;
    refundedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}>;
export declare function getPaymentsForUser(userId: string, requestingUserId: string, filters?: {
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    paymentMethod?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getPaymentById(paymentId: string, requestingUserId: string): Promise<Payment>;
export declare function updatePayment(paymentId: string, updates: Partial<Payment>, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function getTrainerRevenue(trainerId: string, requestingUserId: string, filters?: {
    periodType?: 'daily' | 'weekly' | 'monthly' | 'yearly';
    dateFrom?: string;
    dateTo?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function calculateRevenue(trainerId: string, period: string, periodType: 'daily' | 'weekly' | 'monthly' | 'yearly', requestingUserId: string): Promise<Revenue | null>;
export declare function processRefund(paymentId: string, refundAmount: number, refundReason: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
//# sourceMappingURL=payments-crud.d.ts.map