import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

export interface Payment {
  _id?: ObjectId;
  id: string;
  trainerId: string;
  clientId: string;
  sessionId?: string; // If payment is for a specific session
  amount: number; // in cents (e.g., $10.00 = 1000)
  currency: string; // e.g., "USD"
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  paymentMethod: 'credit_card' | 'debit_card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer' | 'cash';
  paymentIntentId?: string; // Stripe payment intent ID
  transactionId?: string; // External transaction ID
  description: string;
  metadata?: {
    sessionDate?: string;
    sessionType?: string;
    customRateId?: string;
  };
  fees?: {
    platformFee: number; // Platform fee in cents
    processingFee: number; // Payment processor fee in cents
    trainerEarnings: number; // Amount trainer receives in cents
  };
  refundAmount?: number; // If partially refunded
  refundReason?: string;
  paidAt?: Date;
  refundedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Revenue {
  _id?: ObjectId;
  id: string;
  trainerId: string;
  period: string; // e.g., "2024-01" for monthly, "2024-W01" for weekly
  periodType: 'daily' | 'weekly' | 'monthly' | 'yearly';
  totalEarnings: number; // in cents
  totalSessions: number;
  totalClients: number;
  averageSessionRate: number; // in cents
  platformFees: number; // in cents
  processingFees: number; // in cents
  netEarnings: number; // in cents (after fees)
  paymentBreakdown: {
    credit_card: number;
    paypal: number;
    cash: number;
    other: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

// CREATE a payment
export async function createPayment(
  paymentData: Omit<Payment, '_id' | 'id' | 'createdAt' | 'updatedAt'>, 
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Verify user can create this payment (must be client or trainer involved)
  if (paymentData.trainerId !== requestingUserId && paymentData.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only create payments you are involved in');
  }
  
  const paymentId = new ObjectId().toString();
  const payment: Payment = {
    ...paymentData,
    id: paymentId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('payments').insertOne(payment);
  return { ...payment, _id: result.insertedId };
}

// READ payments for a user
export async function getPaymentsForUser(
  userId: string, 
  requestingUserId: string,
  filters?: {
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    paymentMethod?: string;
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only access their own payments
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own payments');
  }
  
  const query: any = {
    $or: [
      { trainerId: userId },
      { clientId: userId }
    ]
  };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.dateFrom || filters?.dateTo) {
    query.createdAt = {};
    if (filters.dateFrom) {
      query.createdAt.$gte = new Date(filters.dateFrom);
    }
    if (filters.dateTo) {
      query.createdAt.$lte = new Date(filters.dateTo);
    }
  }
  
  if (filters?.paymentMethod) {
    query.paymentMethod = filters.paymentMethod;
  }
  
  return db.collection('payments').find(query).sort({ createdAt: -1 }).toArray();
}

// READ a specific payment
export async function getPaymentById(paymentId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const payment = await db.collection('payments').findOne({ id: paymentId }) as Payment | null;
  
  if (!payment) {
    throw new Error('Payment not found');
  }
  
  // Verify user can access this payment
  if (payment.trainerId !== requestingUserId && payment.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only access payments you are involved in');
  }
  
  return payment;
}

// UPDATE a payment
export async function updatePayment(paymentId: string, updates: Partial<Payment>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the payment exists and user has access
  const existingPayment = await getPaymentById(paymentId, requestingUserId);
  
  // Remove fields that shouldn't be updated directly
  const { _id, id, trainerId, clientId, createdAt, ...safeUpdates } = updates;
  
  const updateData = {
    ...safeUpdates,
    updatedAt: new Date(),
  };
  
  const result = await db.collection('payments').updateOne(
    { id: paymentId },
    { $set: updateData }
  );
  
  return result;
}

// Get trainer earnings/revenue
export async function getTrainerRevenue(
  trainerId: string, 
  requestingUserId: string,
  filters?: {
    periodType?: 'daily' | 'weekly' | 'monthly' | 'yearly';
    dateFrom?: string;
    dateTo?: string;
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the trainer can access their revenue
  if (trainerId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own revenue data');
  }
  
  const query: any = { trainerId };
  
  // Apply filters
  if (filters?.periodType) {
    query.periodType = filters.periodType;
  }
  
  if (filters?.dateFrom || filters?.dateTo) {
    query.createdAt = {};
    if (filters.dateFrom) {
      query.createdAt.$gte = new Date(filters.dateFrom);
    }
    if (filters.dateTo) {
      query.createdAt.$lte = new Date(filters.dateTo);
    }
  }
  
  return db.collection('revenue').find(query).sort({ period: -1 }).toArray();
}

// Calculate and store revenue for a period
export async function calculateRevenue(
  trainerId: string, 
  period: string, 
  periodType: 'daily' | 'weekly' | 'monthly' | 'yearly',
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the trainer can calculate their revenue
  if (trainerId !== requestingUserId) {
    throw new Error('Access denied: You can only calculate your own revenue');
  }
  
  // Get date range for the period
  const { startDate, endDate } = getPeriodDateRange(period, periodType);
  
  // Aggregate payments for the period
  const pipeline = [
    {
      $match: {
        trainerId,
        status: 'completed',
        paidAt: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: null,
        totalEarnings: { $sum: '$amount' },
        totalSessions: { $sum: 1 },
        totalClients: { $addToSet: '$clientId' },
        platformFees: { $sum: '$fees.platformFee' },
        processingFees: { $sum: '$fees.processingFee' },
        netEarnings: { $sum: '$fees.trainerEarnings' },
        paymentMethods: { $push: '$paymentMethod' }
      }
    }
  ];
  
  const result = await db.collection('payments').aggregate(pipeline).toArray();
  
  if (result.length === 0) {
    return null; // No payments for this period
  }
  
  const data = result[0];
  
  // Calculate payment breakdown
  const paymentBreakdown = {
    credit_card: 0,
    paypal: 0,
    cash: 0,
    other: 0
  };
  
  data.paymentMethods.forEach((method: string) => {
    if (method === 'credit_card' || method === 'debit_card') {
      paymentBreakdown.credit_card++;
    } else if (method === 'paypal') {
      paymentBreakdown.paypal++;
    } else if (method === 'cash') {
      paymentBreakdown.cash++;
    } else {
      paymentBreakdown.other++;
    }
  });
  
  const revenueId = new ObjectId().toString();
  const revenue: Revenue = {
    id: revenueId,
    trainerId,
    period,
    periodType,
    totalEarnings: data.totalEarnings || 0,
    totalSessions: data.totalSessions || 0,
    totalClients: data.totalClients ? data.totalClients.length : 0,
    averageSessionRate: data.totalSessions > 0 ? Math.round(data.totalEarnings / data.totalSessions) : 0,
    platformFees: data.platformFees || 0,
    processingFees: data.processingFees || 0,
    netEarnings: data.netEarnings || 0,
    paymentBreakdown,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  // Upsert revenue record
  await db.collection('revenue').replaceOne(
    { trainerId, period, periodType },
    revenue,
    { upsert: true }
  );
  
  return revenue;
}

// Helper function to get date range for a period
function getPeriodDateRange(period: string, periodType: string): { startDate: Date; endDate: Date } {
  const now = new Date();
  let startDate: Date;
  let endDate: Date;
  
  switch (periodType) {
    case 'daily':
      startDate = new Date(period);
      endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      break;
    case 'weekly':
      // Period format: "2024-W01"
      const [year, week] = period.split('-W');
      startDate = getDateOfWeek(parseInt(year), parseInt(week));
      endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 7);
      break;
    case 'monthly':
      // Period format: "2024-01"
      const [monthYear, month] = period.split('-');
      startDate = new Date(parseInt(monthYear), parseInt(month) - 1, 1);
      endDate = new Date(parseInt(monthYear), parseInt(month), 1);
      break;
    case 'yearly':
      // Period format: "2024"
      startDate = new Date(parseInt(period), 0, 1);
      endDate = new Date(parseInt(period) + 1, 0, 1);
      break;
    default:
      throw new Error('Invalid period type');
  }
  
  return { startDate, endDate };
}

// Helper function to get the date of a specific week
function getDateOfWeek(year: number, week: number): Date {
  const simple = new Date(year, 0, 1 + (week - 1) * 7);
  const dow = simple.getDay();
  const ISOweekStart = simple;
  if (dow <= 4) {
    ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1);
  } else {
    ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay());
  }
  return ISOweekStart;
}

// Process refund
export async function processRefund(
  paymentId: string, 
  refundAmount: number, 
  refundReason: string,
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the payment exists and user has access
  const existingPayment = await getPaymentById(paymentId, requestingUserId);
  
  // Only trainers can process refunds for their payments
  if (existingPayment.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only trainers can process refunds for their payments');
  }
  
  if (existingPayment.status !== 'completed') {
    throw new Error('Can only refund completed payments');
  }
  
  if (refundAmount > existingPayment.amount) {
    throw new Error('Refund amount cannot exceed payment amount');
  }
  
  const result = await db.collection('payments').updateOne(
    { id: paymentId },
    {
      $set: {
        status: refundAmount === existingPayment.amount ? 'refunded' : 'completed',
        refundAmount,
        refundReason,
        refundedAt: new Date(),
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}
