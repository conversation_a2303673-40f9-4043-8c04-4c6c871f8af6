{"collection": "users", "database": "trainfit", "roles": [{"name": "owner", "apply_when": {"userId": "%%user.id"}, "fields": {"_id": {}, "id": {}, "userId": {}, "email": {}, "name": {}, "role": {}, "createdAt": {}, "updatedAt": {}}, "read": true, "write": true, "insert": true, "delete": false}, {"name": "public-read", "apply_when": {}, "fields": {"_id": {}, "id": {}, "email": {}, "name": {}, "role": {}}, "read": true, "write": false, "insert": false, "delete": false}], "filters": []}