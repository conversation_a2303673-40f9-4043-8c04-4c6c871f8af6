"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDateTypeHelpers = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const null_passthrough_1 = require("./null-passthrough");
/** @internal */
function createDateTypeHelpers({ optional }) {
    return {
        toBinding: (0, null_passthrough_1.nullPassthrough)((value) => {
            if (typeof value === "string") {
                // TODO: Consider deprecating this undocumented type coercion
                return binding_1.binding.Timestamp.fromDate(new Date(value));
            }
            else {
                assert_1.assert.instanceOf(value, Date);
                return binding_1.binding.Timestamp.fromDate(value);
            }
        }, optional),
        fromBinding: (0, null_passthrough_1.nullPassthrough)((value) => {
            assert_1.assert.instanceOf(value, binding_1.binding.Timestamp);
            return value.toDate();
        }, optional),
    };
}
exports.createDateTypeHelpers = createDateTypeHelpers;
//# sourceMappingURL=Date.js.map