import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

export interface Session {
  _id?: ObjectId;
  id: string;
  trainerId: string;
  clientId: string;
  date: string; // ISO date string
  startTime: string; // Format: "HH:MM"
  endTime: string; // Format: "HH:MM"
  status: 'pending' | 'scheduled' | 'completed' | 'cancelled' | 'declined';
  type: 'one-on-one' | 'group' | 'virtual' | 'house-call' | 'in-person';
  participantCount?: number;
  notes?: string;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  cost?: number;
  paymentStatus?: 'pending' | 'paid' | 'refunded' | 'failed';
  paymentMethod?: 'credit_card' | 'paypal' | 'bank' | 'cash';
  declineReason?: string;
  isNewClient?: boolean;
  customRateId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// CREATE a session
export async function createSession(sessionData: Omit<Session, '_id' | 'id' | 'createdAt' | 'updatedAt'>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Verify user can create this session (must be trainer or client involved)
  if (sessionData.trainerId !== requestingUserId && sessionData.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only create sessions you are involved in');
  }
  
  const sessionId = new ObjectId().toString();
  const session: Session = {
    ...sessionData,
    id: sessionId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('sessions').insertOne(session);
  return { ...session, _id: result.insertedId };
}

// READ sessions for a user (trainer or client)
export async function getSessionsForUser(userId: string, requestingUserId: string, filters?: {
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  type?: string;
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only access their own sessions
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own sessions');
  }
  
  const query: any = {
    $or: [
      { trainerId: userId },
      { clientId: userId }
    ]
  };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.dateFrom || filters?.dateTo) {
    query.date = {};
    if (filters.dateFrom) {
      query.date.$gte = filters.dateFrom;
    }
    if (filters.dateTo) {
      query.date.$lte = filters.dateTo;
    }
  }
  
  if (filters?.type) {
    query.type = filters.type;
  }
  
  return db.collection('sessions').find(query).sort({ date: 1, startTime: 1 }).toArray();
}

// READ a specific session
export async function getSessionById(sessionId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const session = await db.collection('sessions').findOne({ id: sessionId }) as Session | null;
  
  if (!session) {
    throw new Error('Session not found');
  }
  
  // Verify user can access this session
  if (session.trainerId !== requestingUserId && session.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only access sessions you are involved in');
  }
  
  return session;
}

// UPDATE a session
export async function updateSession(sessionId: string, updates: Partial<Session>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the session exists and user has access
  const existingSession = await getSessionById(sessionId, requestingUserId);
  
  // Remove fields that shouldn't be updated directly
  const { _id, id, createdAt, ...safeUpdates } = updates;
  
  const updateData = {
    ...safeUpdates,
    updatedAt: new Date(),
  };
  
  const result = await db.collection('sessions').updateOne(
    { id: sessionId },
    { $set: updateData }
  );
  
  return result;
}

// DELETE a session
export async function deleteSession(sessionId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the session exists and user has access
  await getSessionById(sessionId, requestingUserId);
  
  const result = await db.collection('sessions').deleteOne({ id: sessionId });
  return result;
}

// Get sessions for a trainer (trainer-specific view)
export async function getTrainerSessions(trainerId: string, requestingUserId: string, filters?: {
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the trainer can access their sessions
  if (trainerId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own sessions');
  }
  
  const query: any = { trainerId };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.dateFrom || filters?.dateTo) {
    query.date = {};
    if (filters.dateFrom) {
      query.date.$gte = filters.dateFrom;
    }
    if (filters.dateTo) {
      query.date.$lte = filters.dateTo;
    }
  }
  
  return db.collection('sessions').find(query).sort({ date: 1, startTime: 1 }).toArray();
}

// Get sessions for a client (client-specific view)
export async function getClientSessions(clientId: string, requestingUserId: string, filters?: {
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the client can access their sessions
  if (clientId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own sessions');
  }
  
  const query: any = { clientId };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.dateFrom || filters?.dateTo) {
    query.date = {};
    if (filters.dateFrom) {
      query.date.$gte = filters.dateFrom;
    }
    if (filters.dateTo) {
      query.date.$lte = filters.dateTo;
    }
  }
  
  return db.collection('sessions').find(query).sort({ date: 1, startTime: 1 }).toArray();
}

// Update session status (for booking flow)
export async function updateSessionStatus(
  sessionId: string, 
  status: Session['status'], 
  requestingUserId: string,
  additionalData?: { declineReason?: string; notes?: string }
) {
  const updates: Partial<Session> = {
    status,
    ...additionalData,
  };
  
  return updateSession(sessionId, updates, requestingUserId);
}
