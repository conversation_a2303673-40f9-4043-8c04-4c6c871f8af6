import { MongoClient, ServerApiVersion } from 'mongodb';

const uri = "mongodb+srv://pacmantillis:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

let client: MongoClient | undefined = undefined;
let clientPromise: Promise<MongoClient>;

declare global {
  // eslint-disable-next-line no-var
  var _mongoClientPromise: Promise<MongoClient> | undefined;
}

if (!global._mongoClientPromise) {
  client = new MongoClient(uri, {
    serverApi: {
      version: ServerApiVersion.v1,
      strict: true,
      deprecationErrors: true,
    },
  });
  global._mongoClientPromise = client.connect();
}
clientPromise = global._mongoClientPromise!;

export default clientPromise;
