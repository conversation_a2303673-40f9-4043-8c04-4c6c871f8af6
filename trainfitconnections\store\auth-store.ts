import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserRole } from '@/types';
import { Alert } from 'react-native';
import { authAPI, userAPI, tokenManager } from '@/lib/api';

interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  profileImage?: string;
  bio?: string;
  specialties?: string[];
  certifications?: string[];
  experience?: string;
  rating?: number;
  reviewCount?: number;
  hourlyRate?: number;
  rateType?: 'hourly' | 'custom';
  customRates?: {
    id: string;
    title: string;
    amount: number;
  }[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  socialLinks?: {
    instagram?: string;
    twitter?: string;
    facebook?: string;
    linkedin?: string;
    website?: string;
  };
  availability?: {
    days: string[];
    hours: {
      start: string;
      end: string;
    };
  };
  isVerified?: boolean;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string, role: UserRole) => Promise<void>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  clearError: () => void;
  getTrainers: () => Promise<User[]>;
  checkAuthStatus: () => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (email: string, token: string, newPassword: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      clearError: () => {
        set({ error: null });
      },
      
      login: async (email, password) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.login(email, password);

          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });

          console.log("Login successful for:", response.user.email);
        } catch (error) {
          console.log("Login error:", error);
          set({
            error: error instanceof Error ? error.message : "Login failed",
            isLoading: false,
            isAuthenticated: false
          });
          throw error;
        }
      },
      
      register: async (name, email, password, role) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.register(name, email, password, role);

          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });

          console.log("Registration successful for:", response.user.email);
        } catch (error) {
          console.log("Registration error:", error);
          set({
            error: error instanceof Error ? error.message : "Registration failed",
            isLoading: false,
            isAuthenticated: false
          });
          throw error;
        }
      },
      
      logout: async () => {
        try {
          await authAPI.logout();
          set({
            user: null,
            isAuthenticated: false,
            error: null,
            isLoading: false
          });
          console.log("Logout successful");
        } catch (error) {
          console.error("Logout error:", error);
          // Still clear local state even if API call fails
          set({
            user: null,
            isAuthenticated: false,
            error: null,
            isLoading: false
          });
        }
      },
      
      updateProfile: async (userData) => {
        set({ isLoading: true, error: null });
        try {
          await userAPI.updateProfile(userData);

          const currentUser = get().user;
          if (!currentUser) {
            throw new Error("No user logged in");
          }

          // Update local user data
          const updatedUser = { ...currentUser, ...userData };

          set({
            user: updatedUser,
            isLoading: false,
            error: null
          });

          console.log("Profile updated successfully for:", updatedUser.email);
        } catch (error) {
          console.log("Profile update error:", error);
          set({
            error: error instanceof Error ? error.message : "Profile update failed",
            isLoading: false
          });
          throw error;
        }
      },

      // Function to get all trainers
      getTrainers: async () => {
        try {
          const trainers = await userAPI.getTrainers();
          console.log(`Found ${trainers.length} registered trainers`);
          return trainers;
        } catch (error) {
          console.error("Error fetching trainers:", error);
          return [];
        }
      },

      // Check authentication status on app start
      checkAuthStatus: async () => {
        set({ isLoading: true });
        try {
          const token = await tokenManager.getToken();
          if (token) {
            const response = await authAPI.validateToken();
            set({
              user: response.user,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null
            });
          }
        } catch (error) {
          console.log("Token validation failed:", error);
          await tokenManager.removeToken();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        }
      },
      
      // Request password reset (placeholder for future implementation)
      requestPasswordReset: async (email) => {
        set({ isLoading: true, error: null });
        try {
          // TODO: Implement password reset with backend API
          await new Promise(resolve => setTimeout(resolve, 1000));

          Alert.alert(
            "Password Reset",
            "Password reset functionality will be implemented with the backend API.",
            [{ text: "OK" }]
          );

          set({ isLoading: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "Password reset request failed",
            isLoading: false
          });
        }
      },

      // Reset password (placeholder for future implementation)
      resetPassword: async (email, token, newPassword) => {
        set({ isLoading: true, error: null });
        try {
          // TODO: Implement password reset with backend API
          await new Promise(resolve => setTimeout(resolve, 1000));

          Alert.alert(
            "Password Reset",
            "Password reset functionality will be implemented with the backend API.",
            [{ text: "OK" }]
          );

          set({ isLoading: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "Password reset failed",
            isLoading: false
          });
        }
      }
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      }),
    }
  )
);