export type IndexDecorator = (target: unknown, memberName: string) => void;
/**
 * Specify that the decorated field should be indexed by Realm.
 * See: [documentation](https://www.mongodb.com/docs/realm/sdk/react-native/examples/define-a-realm-object-model/#index-a-property)
 */
export declare const index: IndexDecorator;
export type MapToDecorator = (propertyName: string) => (target: unknown, memberName: string) => void;
/**
 * Specify that the decorated field should be remapped to a different property name in the Realm database.
 * See: [documentation](https://www.mongodb.com/docs/realm/sdk/react-native/examples/define-a-realm-object-model/#remap-a-property)
 * @param propertyName The name of the property in the Realm database
 */
export declare const mapTo: MapToDecorator;
