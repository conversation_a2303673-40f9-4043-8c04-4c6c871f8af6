"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.createArrayTypeHelpers = void 0;
const binding_1 = require("../binding");
const assert_1 = require("../assert");
const indirect_1 = require("../indirect");
/** @internal */
function createArrayTypeHelpers({ realm, getClassHelpers, name, objectSchemaName }) {
    assert_1.assert.string(objectSchemaName, "objectSchemaName");
    const classHelpers = getClassHelpers(objectSchemaName);
    return {
        fromBinding(value) {
            assert_1.assert.instanceOf(value, binding_1.binding.List);
            const propertyHelpers = classHelpers.properties.get(name);
            const { listAccessor } = propertyHelpers;
            assert_1.assert.object(listAccessor);
            return new indirect_1.indirect.List(realm, value, listAccessor, propertyHelpers);
        },
        toBinding() {
            throw new Error("Not supported");
        },
    };
}
exports.createArrayTypeHelpers = createArrayTypeHelpers;
//# sourceMappingURL=Array.js.map