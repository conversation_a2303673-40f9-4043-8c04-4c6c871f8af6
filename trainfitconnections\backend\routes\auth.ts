import { Hono } from 'hono';
import { registerUser, loginUser } from '../lib/auth';
import { authMiddleware, getCurrentUser } from '../middleware/auth';

const auth = new Hono();

// Register endpoint
auth.post('/register', async (c) => {
  try {
    const { email, password, name, role } = await c.req.json();
    
    // Validation
    if (!email || !password || !name || !role) {
      return c.json({ error: 'Missing required fields' }, 400);
    }
    
    if (!['client', 'trainer'].includes(role)) {
      return c.json({ error: 'Invalid role. Must be client or trainer' }, 400);
    }
    
    if (password.length < 8) {
      return c.json({ error: 'Password must be at least 8 characters long' }, 400);
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return c.json({ error: 'Invalid email format' }, 400);
    }
    
    const { user, token } = await registerUser(email, password, name, role);
    
    return c.json({
      message: 'User registered successfully',
      user,
      token,
    }, 201);
    
  } catch (error) {
    console.error('Registration error:', error);
    return c.json({ 
      error: error instanceof Error ? error.message : 'Registration failed' 
    }, 400);
  }
});

// Login endpoint
auth.post('/login', async (c) => {
  try {
    const { email, password } = await c.req.json();
    
    // Validation
    if (!email || !password) {
      return c.json({ error: 'Email and password are required' }, 400);
    }
    
    const { user, token } = await loginUser(email, password);
    
    return c.json({
      message: 'Login successful',
      user,
      token,
    });
    
  } catch (error) {
    console.error('Login error:', error);
    return c.json({ 
      error: error instanceof Error ? error.message : 'Login failed' 
    }, 401);
  }
});

// Token validation endpoint
auth.get('/validate', authMiddleware, async (c) => {
  const user = getCurrentUser(c);

  if (!user) {
    return c.json({ error: 'Invalid token' }, 401);
  }

  return c.json({
    message: 'Token is valid',
    user,
  });
});

export { auth };
