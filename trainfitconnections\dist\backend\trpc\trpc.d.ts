import superjson from 'superjson';
export type Context = {
    user?: {
        id: string;
        name: string;
        email: string;
        role: string;
    } | null;
};
export declare const createContext: (opts: {
    req: Request;
}) => Promise<Context>;
export declare const transformer: typeof superjson;
export declare const router: <TInput extends import("@trpc/server/dist/unstable-core-do-not-import").CreateRouterOptions>(input: TInput) => import("@trpc/server/dist/unstable-core-do-not-import").BuiltRouter<{
    ctx: Context;
    meta: object;
    errorShape: {
        data: {
            httpStatus: number;
            code: string;
            message: string;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/dist/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, import("@trpc/server/dist/unstable-core-do-not-import").DecorateCreateRouterOptions<TInput>>;
export declare const publicProcedure: import("@trpc/server/dist/unstable-core-do-not-import").ProcedureBuilder<Context, object, object, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, false>;
export declare const protectedProcedure: import("@trpc/server/dist/unstable-core-do-not-import").ProcedureBuilder<Context, object, {
    user: {
        id: string;
        name: string;
        email: string;
        role: string;
    };
}, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, false>;
//# sourceMappingURL=trpc.d.ts.map