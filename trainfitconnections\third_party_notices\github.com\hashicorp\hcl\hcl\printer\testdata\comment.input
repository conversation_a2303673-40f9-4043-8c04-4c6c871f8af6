// A standalone comment is a comment which is not attached to any kind of node

   // This comes from Terraform, as a test
variable "foo" {
	# Standalone comment should be still here

       default = "bar"
    description =     "bar" # yooo
}

/* This is a multi line standalone
comment*/


// fatih arslan
/* This is a developer test
account and a multine comment */
developer = [     "fatih",       "arslan"] // fatih arslan

# One line here
numbers = [1,2] // another line here

         # Another comment
variable = {
    description =     "bar" # another yooo
    foo { 
	# Nested standalone
	
        bar = "fatih"
    } 
}

          // lead comment
foo { 
    bar = "fatih"       // line comment 2 
}        // line comment 3

multiline = // comment
"assignment"
