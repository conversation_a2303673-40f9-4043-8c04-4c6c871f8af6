import { type Configuration } from "./Configuration";
import type { Realm } from "./Realm";
export declare class ProgressRealmPromise implements Promise<Realm> {
    /** @internal */
    private static instances;
    /**
     * Cancels all unresolved `ProgressRealmPromise` instances.
     * @internal
     */
    static cancelAll(): void;
    /** @internal */
    private handle;
    /** @internal */
    private timeoutPromise;
    /**
     * Token used for unregistering the progress notifier.
     * @internal
     */
    private notifierToken;
    /** @internal */
    constructor(config: Configuration);
    /**
     * Cancels the download of the Realm
     * If multiple `ProgressRealmPromise` instances are in progress for the same Realm, then canceling one of them
     * will cancel all of them.
     */
    cancel(): void;
    then: <TResult1 = Realm, TResult2 = never>(onfulfilled?: ((value: Realm) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null | undefined) => Promise<TResult1 | TResult2>;
    catch: <TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null | undefined) => Promise<Realm | TResult>;
    finally: (onfinally?: (() => void) | null | undefined) => Promise<Realm>;
    /** @internal */
    private rejectAsCanceled;
    get [Symbol.toStringTag](): string;
}
