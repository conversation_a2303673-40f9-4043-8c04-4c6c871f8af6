#!/usr/bin/env node

/**
 * TrainFit Atlas App Services Setup
 * Creates function files that can be easily copied to Atlas dashboard
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 TrainFit Atlas App Services Setup');
console.log('=====================================\n');

// Create functions directory
const functionsDir = path.join(__dirname, 'functions');
if (!fs.existsSync(functionsDir)) {
  fs.mkdirSync(functionsDir, { recursive: true });
}

// Function: auth-register
const authRegisterCode = `exports = function(request, response) {
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }
  
  try {
    const body = JSON.parse(request.body.text());
    const { email, password, name, role } = body;
    
    if (!email || !password || !name || !role) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing required fields" }));
      return;
    }
    
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");
    
    return users.findOne({ email }).then(existingUser => {
      if (existingUser) {
        response.setStatusCode(400);
        response.setBody(JSON.stringify({ error: "User already exists" }));
        return;
      }
      
      const userId = new BSON.ObjectId();
      const user = {
        _id: userId,
        id: userId.toString(),
        email,
        name,
        role,
        password,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      return users.insertOne(user).then(() => {
        const token = \`trainfit_\${userId.toString()}_\${Date.now()}\`;
        
        response.setStatusCode(201);
        response.setBody(JSON.stringify({
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          },
          token
        }));
      });
    }).catch(error => {
      response.setStatusCode(500);
      response.setBody(JSON.stringify({ error: error.message }));
    });
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: "Invalid request body" }));
  }
};`;

// Function: auth-login
const authLoginCode = `exports = function(request, response) {
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }
  
  try {
    const body = JSON.parse(request.body.text());
    const { email, password } = body;
    
    if (!email || !password) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing email or password" }));
      return;
    }
    
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");
    
    return users.findOne({ email }).then(user => {
      if (!user) {
        response.setStatusCode(401);
        response.setBody(JSON.stringify({ error: "Invalid credentials" }));
        return;
      }
      
      if (user.password !== password) {
        response.setStatusCode(401);
        response.setBody(JSON.stringify({ error: "Invalid credentials" }));
        return;
      }
      
      const token = \`trainfit_\${user._id.toString()}_\${Date.now()}\`;
      
      response.setStatusCode(200);
      response.setBody(JSON.stringify({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        token
      }));
    }).catch(error => {
      response.setStatusCode(500);
      response.setBody(JSON.stringify({ error: error.message }));
    });
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: "Invalid request body" }));
  }
};`;

// Function: health-check
const healthCheckCode = `exports = function(request, response) {
  response.setStatusCode(200);
  response.setBody(JSON.stringify({
    status: "ok",
    timestamp: new Date().toISOString(),
    service: "TrainFit Backend",
    version: "1.0.0"
  }));
};`;

// Write function files
fs.writeFileSync(path.join(functionsDir, 'auth-register.js'), authRegisterCode);
fs.writeFileSync(path.join(functionsDir, 'auth-login.js'), authLoginCode);
fs.writeFileSync(path.join(functionsDir, 'health-check.js'), healthCheckCode);

console.log('✅ Function files created successfully!');
console.log('📁 Location: atlas-deploy/functions/');
console.log('');
console.log('📋 Files created:');
console.log('   • auth-register.js');
console.log('   • auth-login.js');
console.log('   • health-check.js');
console.log('');
console.log('🎯 Next Steps:');
console.log('1. Go to Atlas App Services: https://services.cloud.mongodb.com/groups/685878c11ee071769a3fc035/apps/685b8f784722bb4bb71bb804/functions');
console.log('2. Create each function and copy the code from the files above');
console.log('3. Set authentication to "System" for each function');
console.log('4. Create HTTPS endpoints for each function');
console.log('5. Deploy your app');
console.log('');
console.log('🔗 Your Atlas App Services URL:');
console.log('   https://data.mongodb-api.com/app/trainfit-backend-sjcdpns/endpoint');
console.log('');
console.log('✨ Once deployed, your mobile app will work without "network request failed" errors!');
