import { ObjectId } from 'mongodb';
export interface Notification {
    _id?: ObjectId;
    id: string;
    userId: string;
    title: string;
    message: string;
    type: 'session_reminder' | 'session_request' | 'session_cancelled' | 'payment_received' | 'message' | 'workout_assigned' | 'meal_plan_assigned' | 'progress_update' | 'general';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    isRead: boolean;
    readAt?: Date;
    actionUrl?: string;
    actionData?: any;
    relatedEntityId?: string;
    relatedEntityType?: 'session' | 'payment' | 'message' | 'workout_plan' | 'meal_plan';
    scheduledFor?: Date;
    sentAt?: Date;
    expiresAt?: Date;
    channels: ('push' | 'email' | 'sms' | 'in_app')[];
    metadata?: {
        pushToken?: string;
        emailAddress?: string;
        phoneNumber?: string;
    };
    createdAt: Date;
    updatedAt: Date;
}
export interface Reminder {
    _id?: ObjectId;
    id: string;
    userId: string;
    title: string;
    description?: string;
    reminderTime: Date;
    type: 'session' | 'workout' | 'meal' | 'medication' | 'custom';
    isRecurring: boolean;
    recurrencePattern?: {
        frequency: 'daily' | 'weekly' | 'monthly';
        interval: number;
        daysOfWeek?: number[];
        endDate?: Date;
    };
    isActive: boolean;
    lastTriggered?: Date;
    nextTrigger?: Date;
    relatedEntityId?: string;
    relatedEntityType?: 'session' | 'workout_plan' | 'meal_plan';
    createdAt: Date;
    updatedAt: Date;
}
export declare function createNotification(notificationData: Omit<Notification, '_id' | 'id' | 'createdAt' | 'updatedAt' | 'isRead'>, requestingUserId?: string): Promise<{
    _id: ObjectId;
    id: string;
    userId: string;
    title: string;
    message: string;
    type: "session_reminder" | "session_request" | "session_cancelled" | "payment_received" | "message" | "workout_assigned" | "meal_plan_assigned" | "progress_update" | "general";
    priority: "low" | "medium" | "high" | "urgent";
    isRead: boolean;
    readAt?: Date;
    actionUrl?: string;
    actionData?: any;
    relatedEntityId?: string;
    relatedEntityType?: "session" | "payment" | "message" | "workout_plan" | "meal_plan";
    scheduledFor?: Date;
    sentAt?: Date;
    expiresAt?: Date;
    channels: ("push" | "email" | "sms" | "in_app")[];
    metadata?: {
        pushToken?: string;
        emailAddress?: string;
        phoneNumber?: string;
    };
    createdAt: Date;
    updatedAt: Date;
}>;
export declare function getNotificationsForUser(userId: string, requestingUserId: string, filters?: {
    isRead?: boolean;
    type?: string;
    priority?: string;
    limit?: number;
    offset?: number;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function markNotificationAsRead(notificationId: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function markAllNotificationsAsRead(userId: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteNotification(notificationId: string, requestingUserId: string): Promise<import("mongodb").DeleteResult>;
export declare function getUnreadNotificationCount(userId: string, requestingUserId: string): Promise<number>;
export declare function createReminder(reminderData: Omit<Reminder, '_id' | 'id' | 'createdAt' | 'updatedAt' | 'nextTrigger'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    userId: string;
    title: string;
    description?: string;
    reminderTime: Date;
    type: "session" | "workout" | "meal" | "medication" | "custom";
    isRecurring: boolean;
    recurrencePattern?: {
        frequency: "daily" | "weekly" | "monthly";
        interval: number;
        daysOfWeek?: number[];
        endDate?: Date;
    };
    isActive: boolean;
    lastTriggered?: Date;
    nextTrigger?: Date;
    relatedEntityId?: string;
    relatedEntityType?: "session" | "workout_plan" | "meal_plan";
    createdAt: Date;
    updatedAt: Date;
}>;
export declare function getRemindersForUser(userId: string, requestingUserId: string, filters?: {
    type?: string;
    isActive?: boolean;
    upcoming?: boolean;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function updateReminder(reminderId: string, updates: Partial<Reminder>, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteReminder(reminderId: string, requestingUserId: string): Promise<import("mongodb").DeleteResult>;
export declare function getDueReminders(): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function processReminder(reminderId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
//# sourceMappingURL=notifications-crud.d.ts.map