// MongoDB Atlas Function: auth-login
exports = async function(request, response) {
  // Set CORS headers
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // Handle preflight OPTIONS request
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }

  try {
    const { email, password } = JSON.parse(request.body.text());

    // Validate required fields
    if (!email || !password) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing email or password" }));
      return;
    }

    // Get MongoDB service
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");

    // Find user
    const user = await users.findOne({ email });
    if (!user) {
      response.setStatusCode(401);
      response.setBody(JSON.stringify({ error: "Invalid credentials" }));
      return;
    }

    // Verify password (simplified - no hashing for now)
    if (user.password !== password) {
      response.setStatusCode(401);
      response.setBody(JSON.stringify({ error: "Invalid credentials" }));
      return;
    }

    // Create simple token (in production, use proper JWT)
    const token = `trainfit_${user._id.toString()}_${Date.now()}`;

    response.setStatusCode(200);
    response.setBody(JSON.stringify({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      token
    }));

  } catch (error) {
    console.log("Login error:", error);
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: error.message }));
  }
};
