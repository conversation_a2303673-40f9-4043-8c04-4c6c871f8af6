// MongoDB Atlas Function: auth-login
exports = async function(request, response) {
  const { email, password } = JSON.parse(request.body.text());
  
  // Get MongoDB service
  const mongodb = context.services.get("mongodb-atlas");
  const users = mongodb.db("trainfit").collection("users");
  
  try {
    // Find user
    const user = await users.findOne({ email });
    if (!user) {
      response.setStatusCode(401);
      response.setBody(JSON.stringify({ error: "Invalid credentials" }));
      return;
    }
    
    // Verify password
    const bcrypt = require('bcryptjs');
    const isValid = await bcrypt.compare(password, user.passwordHash);
    if (!isValid) {
      response.setStatusCode(401);
      response.setBody(JSON.stringify({ error: "Invalid credentials" }));
      return;
    }
    
    // Generate JWT token
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { userId: user.id, email: user.email, role: user.role },
      context.values.get("JWT_SECRET"),
      { expiresIn: '7d' }
    );
    
    response.setStatusCode(200);
    response.setBody(JSON.stringify({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      token
    }));
    
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: error.message }));
  }
};
