export type LogLevel = "all" | "trace" | "debug" | "detail" | "info" | "warn" | "error" | "fatal" | "off";
/**
 * Log levels used by Realm
 */
export declare enum NumericLogLevel {
    /**
     * Same as 'Trace' but with even more output.
     */
    All = 0,
    /**
     * A version of 'Debug' that allows for very high volume
     * output.
     */
    Trace = 1,
    /**
     * Reveal information that can aid debugging, no longer paying
     * attention to efficiency.
     */
    Debug = 2,
    /**
     * Same as 'Info', but prioritize completeness over minimalism.
     */
    Detail = 3,
    /**
     * Reveal information about what is going on, but in a
     * minimalistic fashion to avoid general overhead from logging
     * and to keep volume down.
     */
    Info = 4,
    /**
     * Be silent unless when there is an error or a warning.
     */
    Warn = 5,
    /**
     * Be silent unless when there is an error.
     */
    Error = 6,
    /**
     * Be silent unless when an error is fatal.
     */
    Fatal = 7,
    /**
     * Be silent.
     */
    Off = 8
}
export declare const LOG_CATEGORIES: readonly ["Realm", "Realm.Storage", "Realm.Storage.Transaction", "Realm.Storage.Query", "Realm.Storage.Object", "Realm.Storage.Notification", "Realm.Sync", "Realm.Sync.Client", "Realm.Sync.Client.Session", "Realm.Sync.Client.Changeset", "Realm.Sync.Client.Network", "Realm.Sync.Client.Reset", "Realm.Sync.Server", "Realm.App", "Realm.SDK"];
/**
 * The category to receive log messages for. The {@link LogLevel} will
 * always be set for a specific category. Setting the log level on one
 * category, will automatically set the log level for any subcategory.
 * @note
 * When debugging, you might not need log messages from everything. To narrow
 * this scope, log events can be grouped by category.
 *
 * `"Realm"`
 * : Include logs from all categories.
 *
 * `"Realm.Storage"`
 * : Database mutations and query operations.
 *
 * `"Realm.Storage.Transaction"`
 * : Creating, advancing, and committing transactions.
 *
 * `"Realm.Storage.Query"`
 * : Query operations.
 *
 * `"Realm.Storage.Object"`
 * : Database mutations.
 *
 * `"Realm.Storage.Notification"`
 * : Notifications of changes to the database.
 *
 * `"Realm.Sync"`
 * : Activity related to Atlas Device Sync.
 *
 * `"Realm.Sync.Client"`
 * : Activity related to Atlas Device Sync client operations.
 *
 * `"Realm.Sync.Client.Session"`
 * : Connection level activity.
 *
 * `"Realm.Sync.Client.Changeset"`
 * : Receiving, uploading, and integrating changesets.
 *
 * `"Realm.Sync.Client.Network"`
 * : Low level network activity.
 *
 * `"Realm.Sync.Client.Reset"`
 * : Client reset operations.
 *
 * `"Realm.Sync.Server"`
 * : Activity related to Atlas Device Sync server operations.
 *
 * `"Realm.App"`
 * : Log activity at the Atlas App level.
 *
 * `"Realm.SDK"`
 * : Log activity at the SDK level.
 */
export type LogCategory = (typeof LOG_CATEGORIES)[number];
/**
 * A callback passed to {@link Realm.setLogger} when instrumenting the Atlas Device Sync client with a custom logger.
 * @param level - The level of the log entry between 0 and 8 inclusively.
 * Use this as an index into `['all', 'trace', 'debug', 'detail', 'info', 'warn', 'error', 'fatal', 'off']` to get the name of the level.
 * @param message - The message of the log entry.
 */
export type Logger = (level: NumericLogLevel, message: string) => void;
/**
 * A callback to be used as the logger.
 * @param level   - The level of the log entry.
 * @param message - The message of the log entry.
 * @since 12.0.0
 * @deprecated Will be removed in v13.0.0
 */
export type LoggerCallback1 = (level: LogLevel, message: string) => void;
/**
 * Represents an entry in the log.
 */
export type LogEntry = {
    /**
     * The category (origin) of the log entry.
     */
    category: LogCategory;
    /**
     * The level of the log entry.
     */
    level: LogLevel;
    /**
     * The message of the log entry.
     */
    message: string;
};
/**
 * A callback to be used as the logger.
 * @since 12.7.0
 */
export type LoggerCallback2 = (entry: LogEntry) => void;
/**
 * A callback to be used as the logger.
 * @since 12.7.0
 */
export type LoggerCallback = LoggerCallback1 | LoggerCallback2;
//# sourceMappingURL=Logger.d.ts.map