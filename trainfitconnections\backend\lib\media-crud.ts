import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

export interface MediaFile {
  _id?: ObjectId;
  id: string;
  ownerId: string; // User who uploaded the file
  fileName: string;
  originalName: string;
  mimeType: string;
  fileSize: number; // in bytes
  url: string; // URL to access the file
  thumbnailUrl?: string; // For images/videos
  type: 'image' | 'video' | 'audio' | 'document';
  category: 'profile_photo' | 'progress_photo' | 'workout_video' | 'meal_photo' | 'document' | 'other';
  description?: string;
  tags?: string[];
  isPublic: boolean; // If true, can be viewed by others (e.g., trainer profile photos)
  sharedWith?: string[]; // Array of user IDs who can access this file
  metadata?: {
    width?: number;
    height?: number;
    duration?: number; // For videos/audio in seconds
    location?: {
      latitude: number;
      longitude: number;
    };
    capturedAt?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  isDeleted?: boolean;
}

export interface ProgressPhoto {
  _id?: ObjectId;
  id: string;
  clientId: string;
  trainerId?: string; // Trainer who can view this photo
  mediaFileId: string; // Reference to MediaFile
  date: string; // ISO date string
  weight?: number;
  bodyFatPercentage?: number;
  measurements?: {
    chest?: number;
    waist?: number;
    hips?: number;
    arms?: number;
    thighs?: number;
    neck?: number;
  };
  notes?: string;
  isVisible: boolean; // If client wants to share with trainer
  createdAt: Date;
  updatedAt: Date;
}

// CREATE a media file
export async function createMediaFile(
  fileData: Omit<MediaFile, '_id' | 'id' | 'createdAt' | 'updatedAt'>, 
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Verify user can upload this file (must be the owner)
  if (fileData.ownerId !== requestingUserId) {
    throw new Error('Access denied: You can only upload files as yourself');
  }
  
  const fileId = new ObjectId().toString();
  const mediaFile: MediaFile = {
    ...fileData,
    id: fileId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('media_files').insertOne(mediaFile);
  return { ...mediaFile, _id: result.insertedId };
}

// READ media files for a user
export async function getMediaFilesForUser(
  userId: string, 
  requestingUserId: string,
  filters?: {
    type?: string;
    category?: string;
    isPublic?: boolean;
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  let query: any = {
    isDeleted: { $ne: true }
  };
  
  // Users can access:
  // 1. Their own files
  // 2. Public files
  // 3. Files shared with them
  if (userId === requestingUserId) {
    // Own files
    query.ownerId = userId;
  } else {
    // Public files or files shared with requesting user
    query.$or = [
      { isPublic: true, ownerId: userId },
      { sharedWith: requestingUserId, ownerId: userId }
    ];
  }
  
  // Apply filters
  if (filters?.type) {
    query.type = filters.type;
  }
  
  if (filters?.category) {
    query.category = filters.category;
  }
  
  if (filters?.isPublic !== undefined) {
    query.isPublic = filters.isPublic;
  }
  
  return db.collection('media_files').find(query).sort({ createdAt: -1 }).toArray();
}

// READ a specific media file
export async function getMediaFileById(fileId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const file = await db.collection('media_files').findOne({ 
    id: fileId,
    isDeleted: { $ne: true }
  }) as MediaFile | null;
  
  if (!file) {
    throw new Error('Media file not found');
  }
  
  // Check access permissions
  const hasAccess = 
    file.ownerId === requestingUserId || // Owner
    file.isPublic || // Public file
    (file.sharedWith && file.sharedWith.includes(requestingUserId)); // Shared with user
  
  if (!hasAccess) {
    throw new Error('Access denied: You do not have permission to access this file');
  }
  
  return file;
}

// UPDATE a media file
export async function updateMediaFile(fileId: string, updates: Partial<MediaFile>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the file exists and user has access
  const existingFile = await getMediaFileById(fileId, requestingUserId);
  
  // Only the owner can update the file
  if (existingFile.ownerId !== requestingUserId) {
    throw new Error('Access denied: Only the file owner can update this file');
  }
  
  // Remove fields that shouldn't be updated directly
  const { _id, id, ownerId, createdAt, ...safeUpdates } = updates;
  
  const updateData = {
    ...safeUpdates,
    updatedAt: new Date(),
  };
  
  const result = await db.collection('media_files').updateOne(
    { id: fileId },
    { $set: updateData }
  );
  
  return result;
}

// DELETE a media file (soft delete)
export async function deleteMediaFile(fileId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the file exists and user has access
  const existingFile = await getMediaFileById(fileId, requestingUserId);
  
  // Only the owner can delete the file
  if (existingFile.ownerId !== requestingUserId) {
    throw new Error('Access denied: Only the file owner can delete this file');
  }
  
  const result = await db.collection('media_files').updateOne(
    { id: fileId },
    {
      $set: {
        isDeleted: true,
        deletedAt: new Date(),
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}

// Share a media file with specific users
export async function shareMediaFile(fileId: string, userIds: string[], requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the file exists and user has access
  const existingFile = await getMediaFileById(fileId, requestingUserId);
  
  // Only the owner can share the file
  if (existingFile.ownerId !== requestingUserId) {
    throw new Error('Access denied: Only the file owner can share this file');
  }
  
  const result = await db.collection('media_files').updateOne(
    { id: fileId },
    {
      $addToSet: { sharedWith: { $each: userIds } },
      $set: { updatedAt: new Date() }
    }
  );
  
  return result;
}

// CREATE a progress photo
export async function createProgressPhoto(
  photoData: Omit<ProgressPhoto, '_id' | 'id' | 'createdAt' | 'updatedAt'>, 
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Verify user can create this progress photo (must be the client)
  if (photoData.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only create progress photos for yourself');
  }
  
  const photoId = new ObjectId().toString();
  const progressPhoto: ProgressPhoto = {
    ...photoData,
    id: photoId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('progress_photos').insertOne(progressPhoto);
  return { ...progressPhoto, _id: result.insertedId };
}

// READ progress photos for a client
export async function getProgressPhotosForClient(
  clientId: string, 
  requestingUserId: string,
  filters?: {
    dateFrom?: string;
    dateTo?: string;
    isVisible?: boolean;
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can access progress photos if:
  // 1. They are the client
  // 2. They are the trainer and photos are visible
  let query: any = { clientId };
  
  if (clientId !== requestingUserId) {
    // If not the client, must be trainer and photos must be visible
    query.trainerId = requestingUserId;
    query.isVisible = true;
  }
  
  // Apply filters
  if (filters?.dateFrom || filters?.dateTo) {
    query.date = {};
    if (filters.dateFrom) {
      query.date.$gte = filters.dateFrom;
    }
    if (filters.dateTo) {
      query.date.$lte = filters.dateTo;
    }
  }
  
  if (filters?.isVisible !== undefined && clientId === requestingUserId) {
    query.isVisible = filters.isVisible;
  }
  
  return db.collection('progress_photos').find(query).sort({ date: -1 }).toArray();
}

// UPDATE a progress photo
export async function updateProgressPhoto(photoId: string, updates: Partial<ProgressPhoto>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const existingPhoto = await db.collection('progress_photos').findOne({ id: photoId }) as ProgressPhoto | null;
  
  if (!existingPhoto) {
    throw new Error('Progress photo not found');
  }
  
  // Only the client can update their progress photo
  if (existingPhoto.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only update your own progress photos');
  }
  
  // Remove fields that shouldn't be updated directly
  const { _id, id, clientId, createdAt, ...safeUpdates } = updates;
  
  const updateData = {
    ...safeUpdates,
    updatedAt: new Date(),
  };
  
  const result = await db.collection('progress_photos').updateOne(
    { id: photoId },
    { $set: updateData }
  );
  
  return result;
}

// DELETE a progress photo
export async function deleteProgressPhoto(photoId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const existingPhoto = await db.collection('progress_photos').findOne({ id: photoId }) as ProgressPhoto | null;
  
  if (!existingPhoto) {
    throw new Error('Progress photo not found');
  }
  
  // Only the client can delete their progress photo
  if (existingPhoto.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only delete your own progress photos');
  }
  
  const result = await db.collection('progress_photos').deleteOne({ id: photoId });
  return result;
}
