import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Message, MessageThread, Reminder } from '@/types';
import { useAuthStore } from './auth-store';
import { trpcClient } from '@/lib/trpc';
import { messagesAPI } from '@/lib/api';

interface MessageState {
  threads: MessageThread[];
  messages: Message[];
  messageNotifications: Reminder[];
  isLoading: boolean;
  error: string | null;
  hasUnreadMessages: boolean;
  
  // Actions
  fetchThreads: () => Promise<void>;
  fetchMessages: (threadId?: string, recipientId?: string) => Promise<void>;
  sendMessage: (messageData: { content: string; recipientId: string; threadId?: string }) => Promise<void>;
  fetchConversations: () => Promise<void>;
  markThreadAsRead: (threadId: string) => void;
  markMessageNotificationAsRead: (notificationId: string) => void;
  deleteThread: (threadId: string) => void;
}

// Mock message notifications
const mockMessageNotifications: <PERSON>mind<PERSON>[] = [
  {
    id: 'mn1',
    trainerId: 't1',
    clientId: 'c1',
    title: 'New Message',
    message: "Emma Wilson: Thanks for the workout plan! I'll start tomorrow.",
    date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    time: new Date(Date.now() - 2 * 60 * 60 * 1000).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
    isRead: false,
    type: 'message',
    relatedId: 'thread1',
    senderId: 'c1',
    senderName: 'Emma Wilson',
    senderImage: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1000',
  },
];

export const useMessageStore = create<MessageState>()(
  persist(
    (set, get) => ({
      threads: [],
      messages: [],
      messageNotifications: mockMessageNotifications,
      isLoading: false,
      error: null,
      hasUnreadMessages: false,
      
      fetchThreads: async () => {
        set({ isLoading: true, error: null });
        try {
          const { user } = useAuthStore.getState();
          if (!user) {
            throw new Error('User not authenticated');
          }
          
          // Fetch threads from the backend
          const result = await trpcClient.messages.getThreads.query();
          
          if (!result) {
            throw new Error('Invalid response from server');
          }
          
          set({ 
            threads: result.threads || [],
            hasUnreadMessages: result.hasUnreadMessages || false,
            isLoading: false 
          });
          
          return Promise.resolve();
        } catch (error) {
          console.error('Error fetching threads:', error);
          set({ 
            error: error instanceof Error ? error.message : "Failed to fetch message threads", 
            isLoading: false 
          });
          return Promise.reject(error);
        }
      },
      
      fetchMessages: async (threadId, recipientId) => {
        set({ isLoading: true, error: null });
        try {
          const { user } = useAuthStore.getState();
          if (!user) {
            throw new Error('User not authenticated');
          }

          // Fetch messages from the new secure API
          const messages = await messagesAPI.getMessages(threadId || `${user.id}-${recipientId}`);

          set({
            messages,
            isLoading: false
          });

          console.log('Messages fetched successfully via secure API:', messages.length);
          return Promise.resolve();
        } catch (error) {
          console.error('Error fetching messages:', error);
          set({
            error: error instanceof Error ? error.message : "Failed to fetch messages",
            isLoading: false
          });
          return Promise.reject(error);
        }
      },

      fetchConversations: async () => {
        set({ isLoading: true, error: null });
        try {
          const conversations = await messagesAPI.getConversations();

          // Convert conversations to threads format
          const threads: MessageThread[] = conversations.map(conv => ({
            id: conv.id,
            participants: conv.participants,
            lastMessage: conv.lastMessage ? {
              content: conv.lastMessage.content,
              senderId: conv.lastMessage.senderId,
              timestamp: conv.lastMessage.timestamp.toISOString(),
            } : undefined,
            lastActivity: conv.lastActivity.toISOString(),
            unreadCount: 0, // TODO: Calculate unread count
          }));

          set({
            threads,
            isLoading: false
          });

          console.log('Conversations fetched successfully via secure API:', threads.length);
        } catch (error) {
          console.error('Error fetching conversations:', error);
          set({
            error: error instanceof Error ? error.message : "Failed to fetch conversations",
            isLoading: false
          });
        }
      },

      sendMessage: async ({ content, recipientId, threadId }) => {
        set({ isLoading: true, error: null });
        try {
          const { user } = useAuthStore.getState();
          if (!user) {
            throw new Error('User not authenticated');
          }
          
          // Send message to the new secure API
          const messageData = {
            receiverId: recipientId,
            content,
            type: 'text' as const,
            conversationId: threadId || `${user.id}-${recipientId}` // Generate conversation ID if not provided
          };

          const result = await messagesAPI.sendMessage(messageData);

          // Update local messages
          set(state => ({
            messages: [...state.messages, result],
          }));
          
          // Update threads
          const conversationId = result.conversationId;
          if (conversationId) {
            const existingThreadIndex = get().threads.findIndex(t => t.id === conversationId);

            if (existingThreadIndex >= 0) {
              // Update existing thread
              set(state => ({
                threads: state.threads.map(thread =>
                  thread.id === conversationId
                    ? {
                        ...thread,
                        lastMessage: {
                          content: result.content,
                          senderId: result.senderId,
                          timestamp: result.createdAt,
                        },
                        unreadCount: 0, // Reset unread count for sender
                      }
                    : thread
                ),
              }));
            } else {
              // Create new thread
              const newThread: MessageThread = {
                id: conversationId,
                participants: [user.id, recipientId],
                lastMessage: {
                  content: result.content,
                  senderId: result.senderId,
                  timestamp: result.createdAt,
                },
                lastActivity: result.createdAt,
                unreadCount: 0,
              };
              set(state => ({
                threads: [newThread, ...state.threads],
              }));
            }
          }
          
          set({ isLoading: false });

          console.log('Message sent successfully via secure API:', result.id);
          return Promise.resolve({
            message: result,
            threadId: result.conversationId
          });
        } catch (error) {
          console.error('Error sending message:', error);
          set({ 
            error: error instanceof Error ? error.message : "Failed to send message",
            isLoading: false,
          });
          return Promise.reject(error);
        }
      },
      
      markThreadAsRead: (threadId) => {
        try {
          // Mark thread as read in the backend
          trpcClient.messages.markAsRead.mutate({ threadId })
            .catch(error => {
              console.error('Error marking thread as read:', error);
            });
          
          set(state => {
            // Mark all messages in this thread as read
            const updatedMessages = state.messages.map(message => 
              message.receiverId === useAuthStore.getState().user?.id
                ? { ...message, isRead: true }
                : message
            );
            
            // Update thread's unread count and last message read status
            const updatedThreads = state.threads.map(thread => 
              thread.id === threadId
                ? { 
                    ...thread, 
                    unreadCount: 0,
                    lastMessage: thread.lastMessage ? {
                      ...thread.lastMessage,
                      isRead: true
                    } : thread.lastMessage
                  }
                : thread
            );
            
            // Update hasUnreadMessages flag
            const hasUnread = updatedThreads.some(thread => 
              thread.participants.includes(useAuthStore.getState().user?.id || '') && 
              thread.lastMessage && 
              thread.lastMessage.senderId !== useAuthStore.getState().user?.id && 
              !thread.lastMessage.isRead
            );
            
            return {
              messages: updatedMessages,
              threads: updatedThreads,
              hasUnreadMessages: hasUnread,
            };
          });
        } catch (error) {
          console.error('Error marking thread as read:', error);
        }
      },
      
      markMessageNotificationAsRead: (notificationId) => {
        set(state => ({
          messageNotifications: state.messageNotifications.map(notification => 
            notification.id === notificationId
              ? { ...notification, isRead: true }
              : notification
          ),
        }));
      },
      
      deleteThread: (threadId) => {
        set(state => ({
          threads: state.threads.filter(thread => thread.id !== threadId),
          // Also remove messages for this thread
          messages: state.messages.filter(message => {
            const thread = state.threads.find(t => t.id === threadId);
            if (!thread) return true;
            return !(
              thread.participants.includes(message.senderId) && 
              thread.participants.includes(message.receiverId)
            );
          }),
        }));
      },
    }),
    {
      name: 'message-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);