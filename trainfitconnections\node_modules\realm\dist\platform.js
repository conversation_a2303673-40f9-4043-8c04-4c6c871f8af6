"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.garbageCollection = exports.network = exports.fs = exports.binding = void 0;
/** @internal */
var binding_1 = require("./platform/binding");
Object.defineProperty(exports, "binding", { enumerable: true, get: function () { return binding_1.binding; } });
/** @internal */
var file_system_1 = require("./platform/file-system");
Object.defineProperty(exports, "fs", { enumerable: true, get: function () { return file_system_1.fs; } });
/** @internal */
var network_1 = require("./platform/network");
Object.defineProperty(exports, "network", { enumerable: true, get: function () { return network_1.network; } });
/** @internal */
var garbage_collection_1 = require("./platform/garbage-collection");
Object.defineProperty(exports, "garbageCollection", { enumerable: true, get: function () { return garbage_collection_1.garbageCollection; } });
//# sourceMappingURL=platform.js.map