// MongoDB Atlas Function: auth-register
exports = async function(request, response) {
  const { email, password, name, role } = JSON.parse(request.body.text());
  
  // Get MongoDB service
  const mongodb = context.services.get("mongodb-atlas");
  const users = mongodb.db("trainfit").collection("users");
  
  try {
    // Check if user already exists
    const existingUser = await users.findOne({ email });
    if (existingUser) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: "User already exists" })
      };
    }
    
    // Hash password (you'll need to implement this)
    const bcrypt = require('bcryptjs');
    const passwordHash = await bcrypt.hash(password, 12);
    
    // Create user
    const user = {
      id: new BSON.ObjectId().toString(),
      email,
      name,
      role,
      passwordHash,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await users.insertOne(user);
    
    // Generate JWT token (you'll need to implement this)
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { userId: user.id, email: user.email, role: user.role },
      context.values.get("JWT_SECRET"),
      { expiresIn: '7d' }
    );
    
    response.setStatusCode(201);
    response.setBody(JSON.stringify({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      token
    }));
    
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: error.message }));
  }
};
