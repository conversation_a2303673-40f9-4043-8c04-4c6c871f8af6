// MongoDB Atlas Function: auth-register
exports = async function(request, response) {
  // Set CORS headers
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // Handle preflight OPTIONS request
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }

  try {
    const { email, password, name, role } = JSON.parse(request.body.text());

    // Validate required fields
    if (!email || !password || !name || !role) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing required fields" }));
      return;
    }

    // Get MongoDB service
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");

    // Check if user already exists
    const existingUser = await users.findOne({ email });
    if (existingUser) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "User already exists" }));
      return;
    }

    // Create user (simplified - no password hashing for now)
    const userId = new BSON.ObjectId();
    const user = {
      _id: userId,
      id: userId.toString(),
      email,
      name,
      role,
      password, // In production, hash this!
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await users.insertOne(user);

    // Create simple token (in production, use proper JWT)
    const token = `trainfit_${userId.toString()}_${Date.now()}`;

    response.setStatusCode(201);
    response.setBody(JSON.stringify({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      token
    }));

  } catch (error) {
    console.log("Registration error:", error);
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: error.message }));
  }
};
