/**
 * Represents the coordinates of a point. The first two required elements of the array are longitude (index 0) and latitude (index 1).
 * The third and optional element is altitude (index 2), but is currently ignored in the geospatial queries calculations.
 */
export type GeoPosition = [number, number] | [number, number, number];
/**
 * Interface that satisfies the GeoJSON specification for a polygon.
 * This can be used as one of the possible forms of {@link GeoPolygon}.
 */
export interface CanonicalGeoPolygon {
    coordinates: GeoPosition[][];
    type: "Polygon";
}
/**
 * Interface that satisfies the GeoJSON specification for a point.
 * Any embedded object that adhere to this interface can be queried in geospatial queries.
 * Additionally, this can be used as one of the possible forms of {@link GeoPoint}.
 */
export interface CanonicalGeoPoint {
    coordinates: GeoPosition;
    type: "Point";
}
/**
 * Represents a point in spherical geometry.
 * This type cannot be used on its own, only as a building block for the other geospatial types.
 * ({@link GeoCircle}, {@link GeoBox}, {@link GeoPolygon}).
 */
export type GeoPoint = 
/**
 * This is compatible with {@link https://developer.mozilla.org/en-US/docs/Web/API/GeolocationCoordinates|GeoLocationCoordinates}
 */
{
    latitude: number;
    longitude: number;
    altitude?: number;
} | CanonicalGeoPoint | GeoPosition;
/**
 * Represents a circle in spherical geometry that can be used as an argument for geospatial queries.
 * @example
 * let circle: GeoCircle = {
 *   center: [20, 40],
 *   distance: 0.05,
 * };
 * realm.objects(Restaurant).filtered("location geoWithin $0", circle)
 */
export type GeoCircle = {
    /** The center of the circle. */
    center: GeoPoint;
    /**
     * The radius of the circle in radians. You can use {@link kmToRadians} and {@link miToRadians}
     * to respectively convert kilometers and miles to radians.
     */
    distance: number;
};
/**
 * Represents a polygon in spherical geometry that can be used as an argument for geospatial queries.
 * The polygon is comprised of at least one outer ring and optionally multiple internal rings representing holes with the following restrictions:
 * - Each ring must contains at least 3 distinct points, where the first and the last point must be the same to indicate a closed ring (this means that each ring
 * must have at least 4 points).
 * - The interior rings must be entirely inside the outer ring.
 * - Rings can share vertices but not edges.
 * - No ring may be empty.
 * @example
 * let polygon: GeoPolygon = {
 *  outerRing: [
 *   [-2, -2],
 *   [3.45, -4.23],
 *   [2.56, 4.62],
 *   [-3.23, 2.5],
 *   [-2, -2],
 *  ],
 * };
 * realm.objects(Restaurant).filtered("location geoWithin $0", polygon)
 */
export type GeoPolygon = {
    outerRing: GeoPoint[];
    holes?: GeoPoint[][];
} | CanonicalGeoPolygon;
/**
 * Represents a box in spherical geometry that can be used as an argument for geospatial queries.
 * This is a short-hand for the equivalent {@link GeoPolygon}.
 * @example
 * let box: GeoBox = {
 *   bottomLeft: [-1, -1],
 *   topRight: [1, 1],
 * };
 * realm.objects(Restaurant).filtered("location geoWithin $0", box)
 */
export type GeoBox = {
    /** The bottom left point of the box. */
    bottomLeft: GeoPoint;
    /** The top right point of the box. */
    topRight: GeoPoint;
};
/**
 * Converts the input kilometer value in radians.
 * @param km - The kilometers to convert.
 * @returns The corresponding number of radians.
 */
export declare function kmToRadians(km: number): number;
/**
 * Converts the input miles value in radians.
 * @param mi - The miles to convert.
 * @returns The corresponding number of radians.
 */
export declare function miToRadians(mi: number): number;
//# sourceMappingURL=GeoSpatial.d.ts.map