import { binding } from "../binding";
import type { TypeHelpers } from "../TypeHelpers";
import type { Realm } from "../Realm";
import type { List } from "../List";
/** @internal */
export type ListAccessor<T = unknown> = {
    get: (list: binding.List, index: number) => T;
    set: (list: binding.List, index: number, value: T) => void;
    insert: (list: binding.List, index: number, value: T) => void;
};
type ListAccessorFactoryOptions<T> = {
    realm: Realm;
    typeHelpers: TypeHelpers<T>;
    itemType: binding.PropertyType;
    isEmbedded?: boolean;
};
/** @internal */
export declare function createListAccessor<T>(options: ListAccessorFactoryOptions<T>): ListAccessor<T>;
/** @internal */
export declare function insertIntoListOfMixed(list: List | unknown[], internal: binding.List, toBinding: TypeHelpers["toBinding"]): void;
/** @internal */
export declare function isJsOrRealmList(value: unknown): value is List | unknown[];
export {};
