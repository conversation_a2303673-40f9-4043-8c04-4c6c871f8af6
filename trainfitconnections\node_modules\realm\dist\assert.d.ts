import type { binding } from "./binding";
import type { DefaultObject } from "./schema";
import type { Realm } from "./Realm";
/**
 * Expects the condition to be truthy
 * @throws an {@link Error} If the condition is not truthy. Throws either the {@link err} given as param if it's an {@link Error},
 * an {@link AssertionError} wrapping {@link err} if it's a string or undefined, or uses the result of invoking {@link err} if it's a function.
 * @param condition The condition that must be truthy to avoid throwing.
 * @param err Optional message or error to throw.
 * Or a function producing this, which is useful to avoid computing the error message in case it's not needed.
 * @internal
 */
export declare function assert(condition: unknown, err?: string | Error | (() => undefined | string | Error)): asserts condition;
export declare namespace assert {
    export var instanceOf: <T extends Function>(value: unknown, constructor: T, target?: string | undefined) => asserts value is T["prototype"];
    export var string: (value: unknown, target?: string | undefined) => asserts value is string;
    export var number: (value: unknown, target?: string | undefined) => asserts value is number;
    export var integer: (value: unknown, target?: string | undefined) => asserts value is number;
    export var numericString: (value: unknown, target?: string | undefined) => void;
    export var boolean: (value: unknown, target?: string | undefined) => asserts value is boolean;
    var _a: (value: unknown, target?: string | undefined) => asserts value is (...args: unknown[]) => unknown;
    export var symbol: (value: unknown, target?: string | undefined) => asserts value is symbol;
    export var object: <K extends string | number | symbol = string, V = unknown>(value: unknown, target?: string | undefined, { allowArrays }?: {
        allowArrays: boolean;
    }) => asserts value is Record<K, V>;
    export var undefined: (value: unknown, target?: string | undefined) => asserts value is undefined;
    var _b: (value: unknown, target?: string | undefined) => asserts value is null;
    export var array: (value: unknown, target?: string | undefined) => asserts value is unknown[];
    var _c: <T extends Function>(value: unknown, constructor: T, target?: string | undefined) => asserts value is T & DefaultObject;
    export var iterable: (value: unknown, target?: string | undefined) => asserts value is Iterable<unknown>;
    export var never: (value: never, target?: string | undefined) => never;
    export var open: (realm: Realm) => void;
    export var inTransaction: (realm: Realm, message?: string) => void;
    export var outTransaction: (realm: Realm, message?: string) => void;
    export var isValid: (obj: binding.Obj, message?: string) => void;
    export var isSameRealm: (realm1: binding.Realm, realm2: binding.Realm, message?: string) => void;
    export { _a as function, _b as null, _c as extends };
}
