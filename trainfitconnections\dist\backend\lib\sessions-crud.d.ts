import { ObjectId } from 'mongodb';
export interface Session {
    _id?: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    date: string;
    startTime: string;
    endTime: string;
    status: 'pending' | 'scheduled' | 'completed' | 'cancelled' | 'declined';
    type: 'one-on-one' | 'group' | 'virtual' | 'house-call' | 'in-person';
    participantCount?: number;
    notes?: string;
    location?: {
        latitude: number;
        longitude: number;
        address: string;
    };
    cost?: number;
    paymentStatus?: 'pending' | 'paid' | 'refunded' | 'failed';
    paymentMethod?: 'credit_card' | 'paypal' | 'bank' | 'cash';
    declineReason?: string;
    isNewClient?: boolean;
    customRateId?: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare function createSession(sessionData: Omit<Session, '_id' | 'id' | 'createdAt' | 'updatedAt'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    trainerId: string;
    clientId: string;
    date: string;
    startTime: string;
    endTime: string;
    status: "pending" | "scheduled" | "completed" | "cancelled" | "declined";
    type: "one-on-one" | "group" | "virtual" | "house-call" | "in-person";
    participantCount?: number;
    notes?: string;
    location?: {
        latitude: number;
        longitude: number;
        address: string;
    };
    cost?: number;
    paymentStatus?: "pending" | "paid" | "refunded" | "failed";
    paymentMethod?: "credit_card" | "paypal" | "bank" | "cash";
    declineReason?: string;
    isNewClient?: boolean;
    customRateId?: string;
    createdAt: Date;
    updatedAt: Date;
}>;
export declare function getSessionsForUser(userId: string, requestingUserId: string, filters?: {
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    type?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getSessionById(sessionId: string, requestingUserId: string): Promise<Session>;
export declare function updateSession(sessionId: string, updates: Partial<Session>, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteSession(sessionId: string, requestingUserId: string): Promise<import("mongodb").DeleteResult>;
export declare function getTrainerSessions(trainerId: string, requestingUserId: string, filters?: {
    status?: string;
    dateFrom?: string;
    dateTo?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getClientSessions(clientId: string, requestingUserId: string, filters?: {
    status?: string;
    dateFrom?: string;
    dateTo?: string;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function updateSessionStatus(sessionId: string, status: Session['status'], requestingUserId: string, additionalData?: {
    declineReason?: string;
    notes?: string;
}): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
//# sourceMappingURL=sessions-crud.d.ts.map