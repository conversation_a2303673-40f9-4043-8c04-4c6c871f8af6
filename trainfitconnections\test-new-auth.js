// Test the new authentication system
const baseUrl = 'http://localhost:3000';

async function testNewAuth() {
  console.log('🔐 Testing New Secure Authentication System...\n');

  // Test 1: Register a new user
  try {
    console.log('1. Testing user registration...');
    const registerResponse = await fetch(`${baseUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Frontend Test User',
        email: '<EMAIL>',
        password: 'testpassword123',
        role: 'client'
      })
    });
    
    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log('✅ Registration successful:', {
        user: registerData.user.name,
        role: registerData.user.role,
        hasToken: !!registerData.token
      });
    } else {
      const errorData = await registerResponse.json();
      console.log('❌ Registration failed:', errorData.error);
    }
  } catch (error) {
    console.log('❌ Registration error:', error.message);
  }

  // Test 2: Login with the new user
  let token = null;
  try {
    console.log('\n2. Testing login...');
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword123'
      })
    });
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      token = loginData.token;
      console.log('✅ Login successful:', {
        user: loginData.user.name,
        role: loginData.user.role,
        hasToken: !!loginData.token
      });
    } else {
      const errorData = await loginResponse.json();
      console.log('❌ Login failed:', errorData.error);
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
  }

  // Test 3: Validate token
  if (token) {
    try {
      console.log('\n3. Testing token validation...');
      const validateResponse = await fetch(`${baseUrl}/api/auth/validate`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (validateResponse.ok) {
        const validateData = await validateResponse.json();
        console.log('✅ Token validation successful:', {
          user: validateData.user.name,
          role: validateData.user.role
        });
      } else {
        const errorData = await validateResponse.json();
        console.log('❌ Token validation failed:', errorData.error);
      }
    } catch (error) {
      console.log('❌ Token validation error:', error.message);
    }

    // Test 4: Get user profile
    try {
      console.log('\n4. Testing protected user profile endpoint...');
      const profileResponse = await fetch(`${baseUrl}/api/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        console.log('✅ Profile retrieval successful:', {
          name: profileData.name,
          email: profileData.email,
          role: profileData.role
        });
      } else {
        const errorData = await profileResponse.json();
        console.log('❌ Profile retrieval failed:', errorData.error);
      }
    } catch (error) {
      console.log('❌ Profile retrieval error:', error.message);
    }

    // Test 5: Get trainers list
    try {
      console.log('\n5. Testing trainers list endpoint...');
      const trainersResponse = await fetch(`${baseUrl}/api/trainers`);
      
      if (trainersResponse.ok) {
        const trainersData = await trainersResponse.json();
        console.log('✅ Trainers list retrieved:', {
          count: trainersData.length,
          trainers: trainersData.map(t => ({ name: t.name, email: t.email }))
        });
      } else {
        const errorData = await trainersResponse.json();
        console.log('❌ Trainers list failed:', errorData.error);
      }
    } catch (error) {
      console.log('❌ Trainers list error:', error.message);
    }
  }

  console.log('\n🎉 New Authentication System Test Complete!');
  console.log('\n📋 Summary:');
  console.log('- ✅ Secure JWT-based authentication');
  console.log('- ✅ Role-based user registration');
  console.log('- ✅ Token validation and protected endpoints');
  console.log('- ✅ User profile management');
  console.log('- ✅ Public trainer listings');
  console.log('\n🚀 Frontend is ready to use the new secure backend!');
}

testNewAuth();
