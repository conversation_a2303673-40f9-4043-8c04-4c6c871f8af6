{"app_id": "trainfit-backend-sjcdpns", "name": "trainfit-backend", "location": "US-VA", "deployment_model": "GLOBAL", "environment": "production", "functions": [{"name": "auth-register", "private": false, "run_as_system": false}, {"name": "auth-login", "private": false, "run_as_system": false}, {"name": "health-check", "private": false, "run_as_system": false}], "auth": {"providers": {"anon-user": {"name": "anon-user", "type": "anon-user", "disabled": true}, "local-userpass": {"name": "local-userpass", "type": "local-userpass", "config": {"autoConfirm": true, "resetFunctionName": "", "runConfirmationFunction": false, "runResetFunction": false}, "disabled": false}}}, "data_sources": [{"name": "mongodb-atlas", "type": "mongodb-atlas", "config": {"clusterName": "Cluster0", "readPreference": "primary", "wireProtocolEnabled": false}}], "sync": {"development_mode_enabled": false}}