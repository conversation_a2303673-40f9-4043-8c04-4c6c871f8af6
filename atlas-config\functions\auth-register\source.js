// Atlas App Services Function: auth-register
// This function is called directly by the Realm SDK, not via HTTP
exports = async function(userData) {
  try {
    const { email, password, name, role } = userData;

    // Validate required fields
    if (!email || !password || !name || !role) {
      throw new Error("Missing required fields");
    }

    // Get MongoDB service
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");

    // Check if user already exists
    const existingUser = await users.findOne({ email });
    if (existingUser) {
      throw new Error("User already exists");
    }

    // Create user with secure Atlas authentication
    const userId = new BSON.ObjectId();
    const user = {
      _id: userId,
      id: userId.toString(),
      userId: context.user.id, // Atlas user ID for security
      email,
      name,
      role,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await users.insertOne(user);

    // Return user data (no password in response)
    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      token: context.user.accessToken || 'atlas-authenticated'
    };

  } catch (error) {
    console.log("Registration error:", error);
    throw new Error(error.message || "Registration failed");
  }
};
