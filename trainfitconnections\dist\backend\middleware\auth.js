"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = authMiddleware;
exports.optionalAuthMiddleware = optionalAuthMiddleware;
exports.requireRole = requireRole;
exports.getCurrentUser = getCurrentUser;
const auth_1 = require("../lib/auth");
// Authentication middleware
async function authMiddleware(c, next) {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return c.json({ error: 'Missing or invalid authorization header' }, 401);
    }
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    // Verify token
    const payload = (0, auth_1.verifyToken)(token);
    if (!payload) {
        return c.json({ error: 'Invalid or expired token' }, 401);
    }
    // Get user from database
    const user = await (0, auth_1.getUserById)(payload.userId);
    if (!user) {
        return c.json({ error: 'User not found' }, 401);
    }
    // Add user to context
    c.set('user', {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
    });
    await next();
}
// Optional authentication middleware (doesn't fail if no token)
async function optionalAuthMiddleware(c, next) {
    const authHeader = c.req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = (0, auth_1.verifyToken)(token);
        if (payload) {
            const user = await (0, auth_1.getUserById)(payload.userId);
            if (user) {
                c.set('user', {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    role: user.role,
                });
            }
        }
    }
    await next();
}
// Role-based authorization middleware
function requireRole(allowedRoles) {
    return async (c, next) => {
        const user = c.get('user');
        if (!user) {
            return c.json({ error: 'Authentication required' }, 401);
        }
        if (!allowedRoles.includes(user.role)) {
            return c.json({ error: 'Insufficient permissions' }, 403);
        }
        await next();
    };
}
// Helper to get current user from context
function getCurrentUser(c) {
    return c.get('user') || null;
}
//# sourceMappingURL=auth.js.map