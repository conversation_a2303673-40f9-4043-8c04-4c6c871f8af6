import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

export interface Notification {
  _id?: ObjectId;
  id: string;
  userId: string; // User who receives the notification
  title: string;
  message: string;
  type: 'session_reminder' | 'session_request' | 'session_cancelled' | 'payment_received' | 'message' | 'workout_assigned' | 'meal_plan_assigned' | 'progress_update' | 'general';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isRead: boolean;
  readAt?: Date;
  actionUrl?: string; // Deep link or URL to relevant screen
  actionData?: any; // Additional data for the action
  relatedEntityId?: string; // ID of related session, payment, etc.
  relatedEntityType?: 'session' | 'payment' | 'message' | 'workout_plan' | 'meal_plan';
  scheduledFor?: Date; // For scheduled notifications
  sentAt?: Date;
  expiresAt?: Date; // When notification becomes irrelevant
  channels: ('push' | 'email' | 'sms' | 'in_app')[]; // How to deliver notification
  metadata?: {
    pushToken?: string;
    emailAddress?: string;
    phoneNumber?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface Reminder {
  _id?: ObjectId;
  id: string;
  userId: string;
  title: string;
  description?: string;
  reminderTime: Date;
  type: 'session' | 'workout' | 'meal' | 'medication' | 'custom';
  isRecurring: boolean;
  recurrencePattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number; // Every X days/weeks/months
    daysOfWeek?: number[]; // For weekly: [0,1,2,3,4,5,6] (Sunday = 0)
    endDate?: Date;
  };
  isActive: boolean;
  lastTriggered?: Date;
  nextTrigger?: Date;
  relatedEntityId?: string;
  relatedEntityType?: 'session' | 'workout_plan' | 'meal_plan';
  createdAt: Date;
  updatedAt: Date;
}

// CREATE a notification
export async function createNotification(
  notificationData: Omit<Notification, '_id' | 'id' | 'createdAt' | 'updatedAt' | 'isRead'>, 
  requestingUserId?: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const notificationId = new ObjectId().toString();
  const notification: Notification = {
    ...notificationData,
    id: notificationId,
    isRead: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('notifications').insertOne(notification);
  return { ...notification, _id: result.insertedId };
}

// READ notifications for a user
export async function getNotificationsForUser(
  userId: string, 
  requestingUserId: string,
  filters?: {
    isRead?: boolean;
    type?: string;
    priority?: string;
    limit?: number;
    offset?: number;
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only access their own notifications
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own notifications');
  }
  
  const query: any = { 
    userId,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  };
  
  // Apply filters
  if (filters?.isRead !== undefined) {
    query.isRead = filters.isRead;
  }
  
  if (filters?.type) {
    query.type = filters.type;
  }
  
  if (filters?.priority) {
    query.priority = filters.priority;
  }
  
  const limit = filters?.limit || 50;
  const offset = filters?.offset || 0;
  
  return db.collection('notifications')
    .find(query)
    .sort({ createdAt: -1 })
    .skip(offset)
    .limit(limit)
    .toArray();
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const notification = await db.collection('notifications').findOne({ id: notificationId }) as Notification | null;
  
  if (!notification) {
    throw new Error('Notification not found');
  }
  
  // Only the recipient can mark notification as read
  if (notification.userId !== requestingUserId) {
    throw new Error('Access denied: You can only mark your own notifications as read');
  }
  
  const result = await db.collection('notifications').updateOne(
    { id: notificationId },
    {
      $set: {
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}

// Mark all notifications as read for a user
export async function markAllNotificationsAsRead(userId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only mark their own notifications as read
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only mark your own notifications as read');
  }
  
  const result = await db.collection('notifications').updateMany(
    { 
      userId,
      isRead: false
    },
    {
      $set: {
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}

// DELETE a notification
export async function deleteNotification(notificationId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const notification = await db.collection('notifications').findOne({ id: notificationId }) as Notification | null;
  
  if (!notification) {
    throw new Error('Notification not found');
  }
  
  // Only the recipient can delete notification
  if (notification.userId !== requestingUserId) {
    throw new Error('Access denied: You can only delete your own notifications');
  }
  
  const result = await db.collection('notifications').deleteOne({ id: notificationId });
  return result;
}

// Get unread notification count
export async function getUnreadNotificationCount(userId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only check their own unread count
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only check your own unread notifications');
  }
  
  const count = await db.collection('notifications').countDocuments({
    userId,
    isRead: false,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
  
  return count;
}

// CREATE a reminder
export async function createReminder(
  reminderData: Omit<Reminder, '_id' | 'id' | 'createdAt' | 'updatedAt' | 'nextTrigger'>, 
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Verify user can create this reminder (must be for themselves)
  if (reminderData.userId !== requestingUserId) {
    throw new Error('Access denied: You can only create reminders for yourself');
  }
  
  const reminderId = new ObjectId().toString();
  
  // Calculate next trigger time
  const nextTrigger = calculateNextTrigger(reminderData.reminderTime, reminderData.recurrencePattern);
  
  const reminder: Reminder = {
    ...reminderData,
    id: reminderId,
    nextTrigger,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('reminders').insertOne(reminder);
  return { ...reminder, _id: result.insertedId };
}

// READ reminders for a user
export async function getRemindersForUser(
  userId: string, 
  requestingUserId: string,
  filters?: {
    type?: string;
    isActive?: boolean;
    upcoming?: boolean; // Next 24 hours
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only access their own reminders
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own reminders');
  }
  
  const query: any = { userId };
  
  // Apply filters
  if (filters?.type) {
    query.type = filters.type;
  }
  
  if (filters?.isActive !== undefined) {
    query.isActive = filters.isActive;
  }
  
  if (filters?.upcoming) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    query.nextTrigger = { $lte: tomorrow };
  }
  
  return db.collection('reminders').find(query).sort({ nextTrigger: 1 }).toArray();
}

// UPDATE a reminder
export async function updateReminder(reminderId: string, updates: Partial<Reminder>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const existingReminder = await db.collection('reminders').findOne({ id: reminderId }) as Reminder | null;
  
  if (!existingReminder) {
    throw new Error('Reminder not found');
  }
  
  // Only the owner can update the reminder
  if (existingReminder.userId !== requestingUserId) {
    throw new Error('Access denied: You can only update your own reminders');
  }
  
  // Remove fields that shouldn't be updated directly
  const { _id, id, userId, createdAt, ...safeUpdates } = updates;
  
  // Recalculate next trigger if reminder time or recurrence changed
  let nextTrigger = existingReminder.nextTrigger;
  if (safeUpdates.reminderTime || safeUpdates.recurrencePattern) {
    const newReminderTime = safeUpdates.reminderTime || existingReminder.reminderTime;
    const newRecurrencePattern = safeUpdates.recurrencePattern || existingReminder.recurrencePattern;
    nextTrigger = calculateNextTrigger(newReminderTime, newRecurrencePattern);
  }
  
  const updateData = {
    ...safeUpdates,
    nextTrigger,
    updatedAt: new Date(),
  };
  
  const result = await db.collection('reminders').updateOne(
    { id: reminderId },
    { $set: updateData }
  );
  
  return result;
}

// DELETE a reminder
export async function deleteReminder(reminderId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const existingReminder = await db.collection('reminders').findOne({ id: reminderId }) as Reminder | null;
  
  if (!existingReminder) {
    throw new Error('Reminder not found');
  }
  
  // Only the owner can delete the reminder
  if (existingReminder.userId !== requestingUserId) {
    throw new Error('Access denied: You can only delete your own reminders');
  }
  
  const result = await db.collection('reminders').deleteOne({ id: reminderId });
  return result;
}

// Helper function to calculate next trigger time
function calculateNextTrigger(reminderTime: Date, recurrencePattern?: Reminder['recurrencePattern']): Date {
  const now = new Date();
  let nextTrigger = new Date(reminderTime);
  
  // If it's a one-time reminder and the time has passed, return the original time
  if (!recurrencePattern) {
    return nextTrigger;
  }
  
  // For recurring reminders, find the next occurrence
  while (nextTrigger <= now) {
    switch (recurrencePattern.frequency) {
      case 'daily':
        nextTrigger.setDate(nextTrigger.getDate() + recurrencePattern.interval);
        break;
      case 'weekly':
        nextTrigger.setDate(nextTrigger.getDate() + (7 * recurrencePattern.interval));
        break;
      case 'monthly':
        nextTrigger.setMonth(nextTrigger.getMonth() + recurrencePattern.interval);
        break;
    }
    
    // Check if we've passed the end date
    if (recurrencePattern.endDate && nextTrigger > recurrencePattern.endDate) {
      break;
    }
  }
  
  return nextTrigger;
}

// Get due reminders (for background processing)
export async function getDueReminders() {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const now = new Date();
  
  return db.collection('reminders').find({
    isActive: true,
    nextTrigger: { $lte: now }
  }).toArray();
}

// Process a reminder (mark as triggered and schedule next)
export async function processReminder(reminderId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const reminder = await db.collection('reminders').findOne({ id: reminderId }) as Reminder | null;
  
  if (!reminder) {
    throw new Error('Reminder not found');
  }
  
  const now = new Date();
  let updateData: any = {
    lastTriggered: now,
    updatedAt: now,
  };
  
  // If it's recurring, calculate next trigger
  if (reminder.isRecurring && reminder.recurrencePattern) {
    const nextTrigger = calculateNextTrigger(reminder.reminderTime, reminder.recurrencePattern);
    
    // If next trigger is past end date, deactivate reminder
    if (reminder.recurrencePattern.endDate && nextTrigger > reminder.recurrencePattern.endDate) {
      updateData.isActive = false;
      updateData.nextTrigger = null;
    } else {
      updateData.nextTrigger = nextTrigger;
    }
  } else {
    // One-time reminder, deactivate it
    updateData.isActive = false;
    updateData.nextTrigger = null;
  }
  
  const result = await db.collection('reminders').updateOne(
    { id: reminderId },
    { $set: updateData }
  );
  
  return result;
}
