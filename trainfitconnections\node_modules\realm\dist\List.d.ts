import { binding } from "./binding";
import { OrderedCollection } from "./OrderedCollection";
import type { Realm } from "./Realm";
import { type TypeHelpers } from "./TypeHelpers";
import type { ListAccessor } from "./collection-accessors/List";
type PartiallyWriteableArray<T> = Pick<Array<T>, "pop" | "push" | "shift" | "unshift" | "splice">;
/**
 * Instances of this class will be returned when accessing object properties whose type is `"list"`.
 *
 * Lists mostly behave like normal Javascript Arrays, except for that they can
 * only store values of a single type (indicated by the `type` and `optional`
 * properties of the List), and can only be modified inside a {@link Realm.write | write} transaction.
 */
export declare class List<T = unknown> extends OrderedCollection<T, [
    number,
    T
], 
/** @internal */
ListAccessor<T>> implements PartiallyWriteableArray<T> {
    /**
     * The representation in the binding.
     * @internal
     */
    readonly internal: binding.List;
    /** @internal */
    private isEmbedded;
    /** @internal */
    constructor(realm: Realm, internal: binding.List, accessor: ListAccessor<T>, typeHelpers: TypeHelpers<T>);
    /** @internal */
    get(index: number): T;
    /** @internal */
    set(index: number, value: T): void;
    /**
     * Checks if this collection has not been deleted and is part of a valid Realm.
     * @returns `true` if the collection can be safely accessed.
     */
    isValid(): boolean;
    /**
     * @returns The number of values in the list.
     */
    get length(): number;
    /**
     * @throws An {@link Error} as the length property cannot be assigned.
     */
    set length(value: number);
    /**
     * Remove the **last** value from the list and return it.
     * @throws an {@link AssertionError} If not inside a write transaction.
     * @returns The last value or undefined if the list is empty.
     */
    pop(): T | undefined;
    /**
     * Add one or more values to the _end_ of the list.
     * @param items - Values to add to the list.
     * @throws A {TypeError} if a value is not of a type which can be stored in
     * the list, or if an object being added to the list does not match the {@link ObjectSchema} for the list.
     * @throws An {@link AssertionError} if not inside a write transaction.
     * @returns The new length of the list after adding the values.
     */
    push(...items: T[]): number;
    /**
     * Remove the **first** value from the list and return it.
     * @throws An {@link AssertionError} if not inside a write transaction.
     * @returns The first value or `undefined` if the list is empty.
     */
    shift(): T | undefined;
    /**
     * Add one or more values to the _beginning_ of the list.
     * @param items - Values to add to the list.
     * @throws A {TypeError} if a value is not of a type which can be stored in
     * the list, or if an object being added to the list does not match the {@link ObjectSchema} for the list.
     * @throws An {@link AssertionError} if not inside a write transaction.
     * @returns The new length of the list after adding the values.
     */
    unshift(...items: T[]): number;
    /**
     * Changes the contents of the list by removing value and/or inserting new value.
     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/splice Array.prototype.splice}
     * @param start - The start index. If greater than the length of the list,
     * the start index will be set to the length instead. If negative, then the start index
     * will be counted from the end of the list (e.g. `list.length - index`).
     * @param deleteCount - The number of values to remove from the list.
     * If not provided, then all values from the start index through the end of
     * the list will be removed.
     * @returns An array containing the value that were removed from the list. The
     * array is empty if no value were removed.
     */
    splice(start: number, deleteCount?: number): T[];
    /**
     * Changes the contents of the list by removing value and/or inserting new value.
     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/splice Array.prototype.splice}
     * @param start - The start index. If greater than the length of the list,
     * the start index will be set to the length instead. If negative, then the start index
     * will be counted from the end of the list (e.g. `list.length - index`).
     * @param deleteCount - The number of values to remove from the list.
     * If not provided, then all values from the start index through the end of
     * the list will be removed.
     * @param items - Values to insert into the list starting at `index`.
     * @returns An array containing the value that were removed from the list. The
     * array is empty if no value were removed.
     */
    splice(start: number, deleteCount: number, ...items: T[]): T[];
    /**
     * Removes the element of the list at the specified index.
     * @param index - The index of the element to remove.
     * @throws An {@link AssertionError} if not inside a write transaction or the input index is less than 0
     * or greater than or equal to the size of the list.
     */
    remove(index: number): void;
    /**
     * Moves one element of the list from one index to another.
     * @param from - The index of the element to move.
     * @param to - The destination index of the element.
     * @throws An {@link AssertionError} if not inside a write transaction or if any of the input indexes
     * is less than 0 or greater than or equal to the size of the list.
     */
    move(from: number, to: number): void;
    /**
     * Swaps the positions of the elements of the list at two indexes.
     * @param index1 - The index of the first element.
     * @param index2 - The index of the second element.
     * @throws An {@link AssertionError} if not inside a write transaction or if any of the input indexes
     * is less than 0 or greater than or equal to the size of the list.
     */
    swap(index1: number, index2: number): void;
}
export type AnyList = List<any>;
export {};
