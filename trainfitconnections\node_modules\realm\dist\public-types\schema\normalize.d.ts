import type { CanonicalObjectSchema, CanonicalPropertySchema, ObjectSchema, PropertySchema, PropertySchemaShorthand, RealmObjectConstructor } from "../schema";
type PropertyInfo = {
    readonly objectName: string;
    readonly propertyName: string;
    readonly propertySchema: PropertySchema | PropertySchemaShorthand;
    readonly isPrimaryKey?: boolean;
};
/**
 * Transform a validated user-provided Realm schema into its canonical form.
 */
export declare function normalizeRealmSchema(realmSchema: Readonly<(RealmObjectConstructor | ObjectSchema)[]>): CanonicalObjectSchema[];
/**
 * Transform a validated user-provided object schema into its canonical form.
 */
export declare function normalizeObjectSchema(arg: RealmObjectConstructor | ObjectSchema): CanonicalObjectSchema;
/**
 * Transform a user-provided property schema into its canonical form.
 */
export declare function normalizePropertySchema(info: PropertyInfo): CanonicalPropertySchema;
/**
 * Extract the base type and the type argument from a generic string notation.
 */
export declare function extractGeneric(type: string): {
    typeBase: string;
    typeArgument?: string;
};
export {};
//# sourceMappingURL=normalize.d.ts.map