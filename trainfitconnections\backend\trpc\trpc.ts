import { initTRPC } from '@trpc/server';
import superjson from 'superjson';
import { verifyToken, getUserById } from '../lib/auth';

// Create context type with optional user property
export type Context = {
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
  } | null;
};

// Create context function for use in the server
export const createContext = async (opts: { req: Request }): Promise<Context> => {
  const authHeader = opts.req.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { user: null };
  }

  const token = authHeader.substring(7);
  const payload = verifyToken(token);

  if (!payload) {
    return { user: null };
  }

  const user = await getUserById(payload.userId);

  if (!user) {
    return { user: null };
  }

  return {
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
    },
  };
};

// Create a new instance of tRPC with improved error handling
const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    // Log the error for debugging
    console.error('TRPC Error:', error);
    
    // Check if the error is related to payload size
    if (
      error.message.includes('payload') || 
      error.message.includes('size') || 
      error.message.includes('large')
    ) {
      return {
        ...shape,
        data: {
          ...shape.data,
          httpStatus: 413,
          code: 'PAYLOAD_TOO_LARGE',
          message: error.message,
        },
      };
    }
    
    return {
      ...shape,
      data: {
        ...shape.data,
        message: error.message,
      },
    };
  },
});

// Export the transformer for client use
export const transformer = superjson;

// Export the router and procedure helpers
export const router = t.router;
export const publicProcedure = t.procedure;

// Create a middleware for protected routes
const isAuthed = t.middleware(({ next, ctx }) => {
  if (!ctx.user) {
    throw new Error('Authentication required');
  }

  return next({
    ctx: {
      user: ctx.user,
    },
  });
});

// Export the protected procedure
export const protectedProcedure = t.procedure.use(isAuthed);