"use strict";
////////////////////////////////////////////////////////////////////////////
//
// Copyright 2024 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.nullPassthrough = void 0;
/**
 * Adds a branch to a function, which checks for the argument to be null, in which case it returns early.
 * @internal
 */
/* eslint-disable-next-line @typescript-eslint/no-explicit-any -- Using `unknown` here breaks type inference in `binding.PropertyType.Object` `toBinding` from for some reason */
function nullPassthrough(fn, enabled) {
    if (enabled) {
        return ((value, ...rest) => typeof value === "undefined" || value === null ? null : fn.call(this, value, ...rest));
    }
    else {
        return fn;
    }
}
exports.nullPassthrough = nullPassthrough;
//# sourceMappingURL=null-passthrough.js.map