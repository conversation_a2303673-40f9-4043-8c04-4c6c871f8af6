#!/usr/bin/env node

/**
 * Deploy Atlas App Services Configuration
 * Uses the Atlas CLI to deploy the complete app configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Deploying TrainFit Atlas App Services Configuration');
console.log('===================================================');

// Check if Atlas CLI is installed
function checkAtlasCLI() {
  try {
    execSync('atlas --version', { stdio: 'pipe' });
    console.log('✅ Atlas CLI found');
    return true;
  } catch (error) {
    console.log('❌ Atlas CLI not found');
    console.log('');
    console.log('📥 Install Atlas CLI:');
    console.log('Windows: https://www.mongodb.com/docs/atlas/cli/stable/install-atlas-cli/#windows');
    console.log('macOS: brew install mongodb-atlas-cli');
    console.log('Linux: https://www.mongodb.com/docs/atlas/cli/stable/install-atlas-cli/#linux');
    return false;
  }
}

// Check if configuration files exist
function checkConfigFiles() {
  const requiredFiles = [
    'atlas-config/realm_config.json',
    'atlas-config/functions/auth-register/config.json',
    'atlas-config/functions/auth-register/source.js',
    'atlas-config/functions/auth-login/config.json',
    'atlas-config/functions/auth-login/source.js',
    'atlas-config/functions/health-check/config.json',
    'atlas-config/functions/health-check/source.js'
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    console.log('❌ Missing configuration files:');
    missingFiles.forEach(file => console.log(`   • ${file}`));
    return false;
  }
  
  console.log('✅ All configuration files found');
  return true;
}

// Deploy the configuration
async function deployConfiguration() {
  try {
    console.log('🔐 Logging in to Atlas...');
    execSync('atlas auth login', { stdio: 'inherit' });
    
    console.log('📁 Setting project...');
    execSync('atlas config set project_id 685878c11ee071769a3fc035', { stdio: 'inherit' });
    
    console.log('🚀 Deploying app configuration...');
    execSync('atlas app deploy --local atlas-config', { stdio: 'inherit' });
    
    console.log('');
    console.log('🎉 Deployment successful!');
    console.log('');
    console.log('✅ Your Atlas App Services is now configured with:');
    console.log('   • Email/Password authentication');
    console.log('   • Secure database rules');
    console.log('   • Atlas App Services functions');
    console.log('');
    console.log('🔗 Your app URL:');
    console.log('   https://data.mongodb-api.com/app/trainfit-backend-sjcdpns/endpoint');
    console.log('');
    console.log('🎯 Next steps:');
    console.log('1. Test the app with a real device');
    console.log('2. Register test users');
    console.log('3. Verify security and data isolation');
    console.log('4. Deploy to TestFlight');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    console.log('');
    console.log('💡 Manual alternative:');
    console.log('1. Copy function code from atlas-config/functions/');
    console.log('2. Paste into Atlas App Services dashboard');
    console.log('3. Configure authentication manually');
    process.exit(1);
  }
}

// Main execution
async function main() {
  if (!checkAtlasCLI()) {
    process.exit(1);
  }
  
  if (!checkConfigFiles()) {
    process.exit(1);
  }
  
  await deployConfiguration();
}

// Run if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { main };
