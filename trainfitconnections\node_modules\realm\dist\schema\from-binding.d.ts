import { binding } from "../binding";
type BindingObjectSchema = binding.ObjectSchema;
type BindingProperty = binding.Property;
import BindingPropertyType = binding.PropertyType;
import type { CanonicalObjectSchema, CanonicalPropertySchema } from "./types";
/**
 * Get the string representation of a property type's base type (not including flags)
 * @internal
 */
export declare function getTypeName(type: BindingPropertyType, objectType: string | undefined): string;
/**
 * Implements https://github.com/realm/realm-js/blob/v11/src/js_schema.hpp#L433-L478
 * @internal
 */
export declare function fromBindingObjectSchema({ name, computedProperties, persistedProperties, primaryKey, tableType, }: BindingObjectSchema): CanonicalObjectSchema;
/**
 * Implements https://github.com/realm/realm-js/blob/v11/src/js_schema.hpp#L480-L530
 * @internal
 */
export declare function fromBindingPropertySchema(propertySchema: BindingProperty): CanonicalPropertySchema;
/** @internal */
export declare function fromBindingRealmSchema(schema: Readonly<BindingObjectSchema[]>): CanonicalObjectSchema[];
export {};
