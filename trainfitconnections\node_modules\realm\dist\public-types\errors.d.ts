export declare class AssertionError extends <PERSON>rror {
}
export declare class TypeAssertionError extends AssertionError {
}
export declare class IllegalConstructorError extends Error {
    constructor(type: string);
}
export declare class TimeoutError extends <PERSON>rror {
    constructor(message: string);
}
export declare class SchemaParseError extends Error {
}
export declare class ObjectSchemaParseError extends SchemaParseError {
    objectName: string;
}
export declare class PropertySchemaParseError extends SchemaParseError {
    objectName: string;
    propertyName: string;
}
//# sourceMappingURL=errors.d.ts.map