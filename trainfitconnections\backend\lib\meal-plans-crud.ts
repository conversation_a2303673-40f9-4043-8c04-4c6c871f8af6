import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

export interface Food {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  calories: number;
  protein: number; // in grams
  carbs: number; // in grams
  fat: number; // in grams
  notes?: string;
}

export interface Meal {
  id: string;
  name: string;
  description?: string;
  time: string; // Format: "HH:MM"
  foods: Food[];
  notes?: string;
  imageUrl?: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  calories?: number;
  macros?: {
    protein: number; // in grams
    carbs: number; // in grams
    fat: number; // in grams
  };
}

export interface MealPlan {
  _id?: ObjectId;
  id: string;
  trainerId: string;
  clientId: string;
  title: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  meals: Meal[];
  notes?: string;
  status: 'active' | 'completed' | 'archived';
  calorieTarget?: number;
  macroTargets?: {
    protein: number; // in grams
    carbs: number; // in grams
    fat: number; // in grams
  };
  isTemplate?: boolean; // If true, can be reused for multiple clients
  tags?: string[];
  dietaryRestrictions?: string[]; // e.g., "vegetarian", "gluten-free", "dairy-free"
}

// CREATE a meal plan
export async function createMealPlan(
  planData: Omit<MealPlan, '_id' | 'id' | 'createdAt' | 'updatedAt'>, 
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only trainers can create meal plans
  if (planData.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only trainers can create meal plans');
  }
  
  const planId = new ObjectId().toString();
  const mealPlan: MealPlan = {
    ...planData,
    id: planId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('meal_plans').insertOne(mealPlan);
  return { ...mealPlan, _id: result.insertedId };
}

// READ meal plans for a user
export async function getMealPlansForUser(userId: string, requestingUserId: string, filters?: {
  status?: string;
  isTemplate?: boolean;
  dietaryRestrictions?: string[];
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only access their own meal plans
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own meal plans');
  }
  
  const query: any = {
    $or: [
      { trainerId: userId },
      { clientId: userId }
    ]
  };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.isTemplate !== undefined) {
    query.isTemplate = filters.isTemplate;
  }
  
  if (filters?.dietaryRestrictions && filters.dietaryRestrictions.length > 0) {
    query.dietaryRestrictions = { $in: filters.dietaryRestrictions };
  }
  
  return db.collection('meal_plans').find(query).sort({ createdAt: -1 }).toArray();
}

// READ a specific meal plan
export async function getMealPlanById(planId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const plan = await db.collection('meal_plans').findOne({ id: planId }) as MealPlan | null;
  
  if (!plan) {
    throw new Error('Meal plan not found');
  }
  
  // Verify user can access this plan
  if (plan.trainerId !== requestingUserId && plan.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only access meal plans assigned to you');
  }
  
  return plan;
}

// UPDATE a meal plan
export async function updateMealPlan(planId: string, updates: Partial<MealPlan>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the plan exists and user has access
  const existingPlan = await getMealPlanById(planId, requestingUserId);
  
  // Only trainers can update meal plans
  if (existingPlan.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only the trainer who created this plan can update it');
  }
  
  // Remove fields that shouldn't be updated directly
  const { _id, id, createdAt, ...safeUpdates } = updates;
  
  const updateData = {
    ...safeUpdates,
    updatedAt: new Date(),
  };
  
  const result = await db.collection('meal_plans').updateOne(
    { id: planId },
    { $set: updateData }
  );
  
  return result;
}

// DELETE a meal plan
export async function deleteMealPlan(planId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the plan exists and user has access
  const existingPlan = await getMealPlanById(planId, requestingUserId);
  
  // Only trainers can delete meal plans
  if (existingPlan.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only the trainer who created this plan can delete it');
  }
  
  const result = await db.collection('meal_plans').deleteOne({ id: planId });
  return result;
}

// Get meal plans created by a trainer
export async function getTrainerMealPlans(trainerId: string, requestingUserId: string, filters?: {
  status?: string;
  isTemplate?: boolean;
  clientId?: string;
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the trainer can access their meal plans
  if (trainerId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own meal plans');
  }
  
  const query: any = { trainerId };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.isTemplate !== undefined) {
    query.isTemplate = filters.isTemplate;
  }
  
  if (filters?.clientId) {
    query.clientId = filters.clientId;
  }
  
  return db.collection('meal_plans').find(query).sort({ createdAt: -1 }).toArray();
}

// Get meal plans assigned to a client
export async function getClientMealPlans(clientId: string, requestingUserId: string, filters?: {
  status?: string;
  dietaryRestrictions?: string[];
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the client can access their meal plans
  if (clientId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own meal plans');
  }
  
  const query: any = { clientId };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.dietaryRestrictions && filters.dietaryRestrictions.length > 0) {
    query.dietaryRestrictions = { $in: filters.dietaryRestrictions };
  }
  
  return db.collection('meal_plans').find(query).sort({ createdAt: -1 }).toArray();
}

// Assign a meal plan to a client (copy from template or assign existing)
export async function assignMealPlanToClient(
  planId: string, 
  clientId: string, 
  requestingUserId: string,
  customizations?: {
    startDate?: string;
    endDate?: string;
    notes?: string;
    calorieTarget?: number;
    macroTargets?: {
      protein: number;
      carbs: number;
      fat: number;
    };
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Get the original plan
  const originalPlan = await getMealPlanById(planId, requestingUserId);
  
  // Only trainers can assign meal plans
  if (originalPlan.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only trainers can assign meal plans');
  }
  
  // Create a new plan for the client
  const newPlanId = new ObjectId().toString();
  const assignedPlan: MealPlan = {
    ...originalPlan,
    id: newPlanId,
    clientId,
    isTemplate: false,
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...customizations,
  };
  
  // Remove the _id to create a new document
  delete assignedPlan._id;
  
  const result = await db.collection('meal_plans').insertOne(assignedPlan);
  return { ...assignedPlan, _id: result.insertedId };
}
