#!/usr/bin/env node

/**
 * Test Secure Atlas App Services Setup
 * Tests Email/Password authentication and function calls
 */

const Realm = require('realm');

const ATLAS_APP_ID = 'trainfit-backend-sjcdpns';

async function testSecureAtlas() {
  console.log('🔒 Testing Secure Atlas App Services Setup...');
  console.log('===============================================');
  
  try {
    // Initialize the app
    console.log('📱 Initializing Atlas App...');
    const app = new Realm.App({ id: ATLAS_APP_ID });
    console.log('✅ Atlas App initialized');
    
    // Test user registration
    console.log('👤 Testing user registration...');
    const testEmail = `test-${Date.now()}@trainfit.com`;
    const testPassword = 'TestPassword123!';
    
    try {
      // Register user with Atlas Email/Password auth
      await app.emailPasswordAuth.registerUser({
        email: testEmail,
        password: testPassword
      });
      console.log('✅ User registration successful');
      
      // Login the user
      console.log('🔐 Testing user login...');
      const credentials = Realm.Credentials.emailPassword(testEmail, testPassword);
      const user = await app.logIn(credentials);
      console.log('✅ User login successful');
      console.log(`   User ID: ${user.id}`);
      
      // Test calling the auth-register function
      console.log('📞 Testing auth-register function...');
      try {
        const registerResult = await user.functions.callFunction('auth-register', {
          email: testEmail,
          password: testPassword,
          name: 'Test User',
          role: 'client'
        });
        console.log('✅ auth-register function works');
        console.log(`   User created: ${registerResult.user.name}`);
      } catch (funcError) {
        console.log('⚠️  auth-register function test:', funcError.message);
      }
      
      // Test calling the auth-login function
      console.log('📞 Testing auth-login function...');
      try {
        const loginResult = await user.functions.callFunction('auth-login', {
          email: testEmail,
          password: testPassword
        });
        console.log('✅ auth-login function works');
        console.log(`   User logged in: ${loginResult.user.name}`);
      } catch (funcError) {
        console.log('⚠️  auth-login function test:', funcError.message);
      }
      
      // Test database access
      console.log('🗄️  Testing database access...');
      const mongodb = user.mongoClient('mongodb-atlas');
      const db = mongodb.db('trainfit');
      const users = db.collection('users');
      
      const userCount = await users.count();
      console.log('✅ Database access successful');
      console.log(`   Users in database: ${userCount}`);
      
      // Logout
      await user.logOut();
      console.log('✅ Logout successful');
      
    } catch (authError) {
      console.log('⚠️  Authentication test:', authError.message);
      console.log('   This might be expected if Email/Password auth is not enabled yet');
    }
    
    console.log('');
    console.log('🎉 ATLAS SETUP TEST COMPLETE!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Complete the manual setup steps above');
    console.log('2. Enable Email/Password authentication');
    console.log('3. Create the auth functions');
    console.log('4. Deploy your Atlas App Services');
    console.log('5. Run: eas build --platform ios --profile production');
    console.log('');
    console.log('✅ Your TrainFit app will then work for real users!');
    
  } catch (error) {
    console.error('❌ Atlas test failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Verify Atlas App Services app exists');
    console.log('2. Check App ID: trainfit-backend-sjcdpns');
    console.log('3. Ensure MongoDB cluster is running');
    console.log('4. Complete the manual setup steps');
  }
}

testSecureAtlas();
