"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.protectedProcedure = exports.publicProcedure = exports.router = exports.transformer = exports.createContext = void 0;
const server_1 = require("@trpc/server");
const superjson_1 = __importDefault(require("superjson"));
const auth_1 = require("../lib/auth");
// Create context function for use in the server
const createContext = async (opts) => {
    const authHeader = opts.req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return { user: null };
    }
    const token = authHeader.substring(7);
    const payload = (0, auth_1.verifyToken)(token);
    if (!payload) {
        return { user: null };
    }
    const user = await (0, auth_1.getUserById)(payload.userId);
    if (!user) {
        return { user: null };
    }
    return {
        user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
        },
    };
};
exports.createContext = createContext;
// Create a new instance of tRPC with improved error handling
const t = server_1.initTRPC.context().create({
    transformer: superjson_1.default,
    errorFormatter({ shape, error }) {
        // Log the error for debugging
        console.error('TRPC Error:', error);
        // Check if the error is related to payload size
        if (error.message.includes('payload') ||
            error.message.includes('size') ||
            error.message.includes('large')) {
            return {
                ...shape,
                data: {
                    ...shape.data,
                    httpStatus: 413,
                    code: 'PAYLOAD_TOO_LARGE',
                    message: error.message,
                },
            };
        }
        return {
            ...shape,
            data: {
                ...shape.data,
                message: error.message,
            },
        };
    },
});
// Export the transformer for client use
exports.transformer = superjson_1.default;
// Export the router and procedure helpers
exports.router = t.router;
exports.publicProcedure = t.procedure;
// Create a middleware for protected routes
const isAuthed = t.middleware(({ next, ctx }) => {
    if (!ctx.user) {
        throw new Error('Authentication required');
    }
    return next({
        ctx: {
            user: ctx.user,
        },
    });
});
// Export the protected procedure
exports.protectedProcedure = t.procedure.use(isAuthed);
//# sourceMappingURL=trpc.js.map