export declare class AssertionError extends Error {
    /** @internal */
    constructor(message?: string);
}
export declare class TypeAssertionError extends AssertionError {
    private expected;
    private value;
    /** @internal */
    static deriveType(value: unknown): string;
    /**
     * Get an error message for when the target's value is of
     * the wrong type. Single quotes are added around the target
     * string if it does not already contain one.
     * @internal
     */
    private static message;
    /** @internal */
    constructor(/** @internal */ expected: string, /** @internal */ value: unknown, target?: string);
    /** @internal */
    rename(name: string): void;
}
export declare class IllegalConstructorError extends Error {
    constructor(type: string);
}
export declare class TimeoutError extends Error {
    constructor(message: string);
}
export declare class SchemaParseError extends Error {
    /** @internal */
    constructor(message: string);
}
export declare class ObjectSchemaParseError extends SchemaParseError {
    objectName: string;
    /** @internal */
    constructor(message: string, info: {
        objectName: string;
    });
}
export declare class PropertySchemaParseError extends SchemaParseError {
    objectName: string;
    propertyName: string;
    /** @internal */
    constructor(message: string, info: {
        objectName: string;
        propertyName: string;
    });
}
