"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMessage = createMessage;
exports.getMessagesForConversation = getMessagesForConversation;
exports.getConversationsForUser = getConversationsForUser;
exports.markMessageAsRead = markMessageAsRead;
exports.markConversationAsRead = markConversationAsRead;
exports.deleteMessage = deleteMessage;
exports.getUnreadMessageCount = getUnreadMessageCount;
exports.getOrCreateConversation = getOrCreateConversation;
exports.archiveConversation = archiveConversation;
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const dbName = 'trainfitconnections';
// CREATE a message
async function createMessage(messageData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Verify user can send this message (must be the sender)
    if (messageData.senderId !== requestingUserId) {
        throw new Error('Access denied: You can only send messages as yourself');
    }
    const messageId = new mongodb_2.ObjectId().toString();
    const message = {
        ...messageData,
        id: messageId,
        isRead: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    // Insert the message
    const result = await db.collection('messages').insertOne(message);
    // Update or create conversation
    await updateConversation(messageData.conversationId, message);
    return { ...message, _id: result.insertedId };
}
// Helper function to update conversation
async function updateConversation(conversationId, message) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const conversation = await db.collection('conversations').findOne({ id: conversationId });
    if (conversation) {
        // Update existing conversation
        await db.collection('conversations').updateOne({ id: conversationId }, {
            $set: {
                lastMessage: {
                    content: message.content,
                    senderId: message.senderId,
                    timestamp: message.createdAt,
                    type: message.type,
                },
                lastActivity: new Date(),
                updatedAt: new Date(),
            }
        });
    }
    else {
        // Create new conversation
        const newConversation = {
            id: conversationId,
            participants: [message.senderId, message.receiverId],
            lastMessage: {
                content: message.content,
                senderId: message.senderId,
                timestamp: message.createdAt,
                type: message.type,
            },
            lastActivity: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        await db.collection('conversations').insertOne(newConversation);
    }
}
// READ messages for a conversation
async function getMessagesForConversation(conversationId, requestingUserId, options) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify user is part of this conversation
    const conversation = await db.collection('conversations').findOne({ id: conversationId });
    if (!conversation || !conversation.participants.includes(requestingUserId)) {
        throw new Error('Access denied: You are not part of this conversation');
    }
    const query = {
        conversationId,
        isDeleted: { $ne: true }
    };
    if (options?.before) {
        query.createdAt = { $lt: options.before };
    }
    const limit = options?.limit || 50;
    const offset = options?.offset || 0;
    return db.collection('messages')
        .find(query)
        .sort({ createdAt: -1 })
        .skip(offset)
        .limit(limit)
        .toArray();
}
// READ conversations for a user
async function getConversationsForUser(userId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own conversations
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own conversations');
    }
    return db.collection('conversations')
        .find({
        participants: userId,
        isArchived: { $ne: true }
    })
        .sort({ lastActivity: -1 })
        .toArray();
}
// Mark message as read
async function markMessageAsRead(messageId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const message = await db.collection('messages').findOne({ id: messageId });
    if (!message) {
        throw new Error('Message not found');
    }
    // Only the receiver can mark a message as read
    if (message.receiverId !== requestingUserId) {
        throw new Error('Access denied: You can only mark messages sent to you as read');
    }
    const result = await db.collection('messages').updateOne({ id: messageId }, {
        $set: {
            isRead: true,
            readAt: new Date(),
            updatedAt: new Date(),
        }
    });
    return result;
}
// Mark all messages in a conversation as read
async function markConversationAsRead(conversationId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify user is part of this conversation
    const conversation = await db.collection('conversations').findOne({ id: conversationId });
    if (!conversation || !conversation.participants.includes(requestingUserId)) {
        throw new Error('Access denied: You are not part of this conversation');
    }
    const result = await db.collection('messages').updateMany({
        conversationId,
        receiverId: requestingUserId,
        isRead: false
    }, {
        $set: {
            isRead: true,
            readAt: new Date(),
            updatedAt: new Date(),
        }
    });
    return result;
}
// DELETE a message (soft delete)
async function deleteMessage(messageId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const message = await db.collection('messages').findOne({ id: messageId });
    if (!message) {
        throw new Error('Message not found');
    }
    // Only the sender can delete their message
    if (message.senderId !== requestingUserId) {
        throw new Error('Access denied: You can only delete your own messages');
    }
    const result = await db.collection('messages').updateOne({ id: messageId }, {
        $set: {
            isDeleted: true,
            deletedAt: new Date(),
            updatedAt: new Date(),
        }
    });
    return result;
}
// Get unread message count for a user
async function getUnreadMessageCount(userId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only check their own unread count
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only check your own unread messages');
    }
    const count = await db.collection('messages').countDocuments({
        receiverId: userId,
        isRead: false,
        isDeleted: { $ne: true }
    });
    return count;
}
// Create or get conversation ID between two users
async function getOrCreateConversation(userId1, userId2, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // User must be one of the participants
    if (requestingUserId !== userId1 && requestingUserId !== userId2) {
        throw new Error('Access denied: You can only access conversations you are part of');
    }
    // Sort user IDs to ensure consistent conversation ID
    const participants = [userId1, userId2].sort();
    const conversationId = `conv_${participants[0]}_${participants[1]}`;
    // Check if conversation already exists
    let conversation = await db.collection('conversations').findOne({ id: conversationId });
    if (!conversation) {
        // Create new conversation
        const newConversation = {
            id: conversationId,
            participants,
            lastActivity: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        const result = await db.collection('conversations').insertOne(newConversation);
        conversation = { ...newConversation, _id: result.insertedId };
    }
    return conversation;
}
// Archive a conversation
async function archiveConversation(conversationId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify user is part of this conversation
    const conversation = await db.collection('conversations').findOne({ id: conversationId });
    if (!conversation || !conversation.participants.includes(requestingUserId)) {
        throw new Error('Access denied: You are not part of this conversation');
    }
    const result = await db.collection('conversations').updateOne({ id: conversationId }, {
        $set: {
            isArchived: true,
            updatedAt: new Date(),
        }
    });
    return result;
}
//# sourceMappingURL=messages-crud.js.map