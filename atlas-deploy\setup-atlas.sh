#!/bin/bash

# Atlas App Services Setup Script
# This script sets up your Atlas App Services functions using the Atlas CLI

echo "🚀 Setting up Atlas App Services for TrainFit"
echo "=============================================="

# Check if Atlas CLI is installed
if ! command -v atlas &> /dev/null; then
    echo "❌ Atlas CLI not found. Installing..."
    
    # Install Atlas CLI based on OS
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        echo "📥 Installing Atlas CLI for Windows..."
        curl -LO https://fastdl.mongodb.org/mongocli/mongodb-atlas-cli_1.14.0_windows_x86_64.zip
        unzip mongodb-atlas-cli_1.14.0_windows_x86_64.zip
        echo "✅ Atlas CLI downloaded. Please add to PATH and run again."
        exit 1
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "📥 Installing Atlas CLI for macOS..."
        brew install mongodb-atlas-cli
    else
        echo "📥 Installing Atlas CLI for Linux..."
        curl -LO https://fastdl.mongodb.org/mongocli/mongodb-atlas-cli_1.14.0_linux_x86_64.tar.gz
        tar -xzf mongodb-atlas-cli_1.14.0_linux_x86_64.tar.gz
        sudo mv mongodb-atlas-cli_1.14.0_linux_x86_64/bin/atlas /usr/local/bin/
    fi
fi

echo "✅ Atlas CLI found"

# Login to Atlas
echo "🔐 Logging in to Atlas..."
atlas auth login

# Set up the project
echo "📁 Setting up project..."
atlas config set project_id 685878c11ee071769a3fc035

# Create app configuration directory
mkdir -p atlas-app-config/functions
mkdir -p atlas-app-config/http_endpoints

echo "📝 Creating function files..."

# Create auth-register function
cat > atlas-app-config/functions/auth-register.js << 'EOF'
exports = function(request, response) {
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }
  
  try {
    const body = JSON.parse(request.body.text());
    const { email, password, name, role } = body;
    
    if (!email || !password || !name || !role) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing required fields" }));
      return;
    }
    
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");
    
    return users.findOne({ email }).then(existingUser => {
      if (existingUser) {
        response.setStatusCode(400);
        response.setBody(JSON.stringify({ error: "User already exists" }));
        return;
      }
      
      const userId = new BSON.ObjectId();
      const user = {
        _id: userId,
        id: userId.toString(),
        email,
        name,
        role,
        password,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      return users.insertOne(user).then(() => {
        const token = `trainfit_${userId.toString()}_${Date.now()}`;
        
        response.setStatusCode(201);
        response.setBody(JSON.stringify({
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          },
          token
        }));
      });
    }).catch(error => {
      response.setStatusCode(500);
      response.setBody(JSON.stringify({ error: error.message }));
    });
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: "Invalid request body" }));
  }
};
EOF

# Create auth-login function
cat > atlas-app-config/functions/auth-login.js << 'EOF'
exports = function(request, response) {
  response.setHeader("Access-Control-Allow-Origin", "*");
  response.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (request.httpMethod === "OPTIONS") {
    response.setStatusCode(200);
    return;
  }
  
  try {
    const body = JSON.parse(request.body.text());
    const { email, password } = body;
    
    if (!email || !password) {
      response.setStatusCode(400);
      response.setBody(JSON.stringify({ error: "Missing email or password" }));
      return;
    }
    
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");
    
    return users.findOne({ email }).then(user => {
      if (!user) {
        response.setStatusCode(401);
        response.setBody(JSON.stringify({ error: "Invalid credentials" }));
        return;
      }
      
      if (user.password !== password) {
        response.setStatusCode(401);
        response.setBody(JSON.stringify({ error: "Invalid credentials" }));
        return;
      }
      
      const token = `trainfit_${user._id.toString()}_${Date.now()}`;
      
      response.setStatusCode(200);
      response.setBody(JSON.stringify({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        token
      }));
    }).catch(error => {
      response.setStatusCode(500);
      response.setBody(JSON.stringify({ error: error.message }));
    });
  } catch (error) {
    response.setStatusCode(500);
    response.setBody(JSON.stringify({ error: "Invalid request body" }));
  }
};
EOF

# Create health-check function
cat > atlas-app-config/functions/health-check.js << 'EOF'
exports = function(request, response) {
  response.setStatusCode(200);
  response.setBody(JSON.stringify({
    status: "ok",
    timestamp: new Date().toISOString(),
    service: "TrainFit Backend",
    version: "1.0.0"
  }));
};
EOF

echo "✅ Function files created"
echo "📁 Files created in: atlas-app-config/functions/"
echo ""
echo "🎯 Next steps:"
echo "1. Run this script: bash setup-atlas.sh"
echo "2. Or manually copy the function code to Atlas dashboard"
echo "3. Create HTTPS endpoints for each function"
echo ""
echo "🔗 Your Atlas App Services URL:"
echo "https://data.mongodb-api.com/app/trainfit-backend-sjcdpns/endpoint"
