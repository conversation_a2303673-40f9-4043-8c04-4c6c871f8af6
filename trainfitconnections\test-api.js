// Comprehensive TrainFit Collections Test Script
const baseUrl = 'http://localhost:3000';

async function testAllCollections() {
  console.log('🏋️‍♀️ Testing TrainFit Complete Database Collections...\n');

  let clientToken = null;
  let trainerToken = null;
  let clientId = null;
  let trainerId = null;

  // Test health endpoint
  try {
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch(`${baseUrl}/api/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }

  // Test client registration
  try {
    console.log('\n2. Testing client registration...');
    const registerResponse = await fetch(`${baseUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test Client',
        email: '<EMAIL>',
        password: 'securepassword123',
        role: 'client'
      })
    });

    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      clientToken = registerData.token;
      clientId = registerData.user.id;
      console.log('✅ Client registered:', {
        user: registerData.user,
        hasToken: !!registerData.token
      });
    } else {
      const errorData = await registerResponse.json();
      console.log('❌ Client registration failed:', errorData);
    }
  } catch (error) {
    console.log('❌ Client registration error:', error.message);
  }

  // Test trainer registration
  try {
    console.log('\n3. Testing trainer registration...');
    const registerResponse = await fetch(`${baseUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test Trainer',
        email: '<EMAIL>',
        password: 'securepassword123',
        role: 'trainer'
      })
    });

    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      trainerToken = registerData.token;
      trainerId = registerData.user.id;
      console.log('✅ Trainer registered:', {
        user: registerData.user,
        hasToken: !!registerData.token
      });
    } else {
      const errorData = await registerResponse.json();
      console.log('❌ Trainer registration failed:', errorData);
    }
  } catch (error) {
    console.log('❌ Trainer registration error:', error.message);
  }

  // Test login
  try {
    console.log('\n4. Testing login...');
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'securepassword123'
      })
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      clientToken = loginData.token; // Store the token for later use
      console.log('✅ Login successful:', {
        user: loginData.user.name,
        hasToken: !!loginData.token
      });
    } else {
      const errorData = await loginResponse.json();
      console.log('❌ Login failed:', errorData);
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
  }

  // Test protected endpoint without token
  try {
    console.log('\n5. Testing protected endpoint without token...');
    const protectedResponse = await fetch(`${baseUrl}/api/users/me`);

    if (protectedResponse.ok) {
      console.log('❌ SECURITY ISSUE: Protected endpoint accessible without token!');
    } else {
      const errorData = await protectedResponse.json();
      console.log('✅ Protected endpoint properly secured:', errorData);
    }
  } catch (error) {
    console.log('❌ Protected endpoint test error:', error.message);
  }

  // Test protected endpoint with valid token
  if (clientToken) {
    try {
      console.log('\n6. Testing protected endpoint with valid token...');
      const protectedResponse = await fetch(`${baseUrl}/api/users/me`, {
        headers: {
          'Authorization': `Bearer ${clientToken}`
        }
      });

      if (protectedResponse.ok) {
        const userData = await protectedResponse.json();
        console.log('✅ Protected endpoint accessible with token:', userData.name);
      } else {
        const errorData = await protectedResponse.json();
        console.log('❌ Protected endpoint failed with valid token:', errorData);
      }
    } catch (error) {
      console.log('❌ Protected endpoint test error:', error.message);
    }
  }

  // Test token validation
  if (clientToken) {
    try {
      console.log('\n7. Testing token validation...');
      const validateResponse = await fetch(`${baseUrl}/api/auth/validate`, {
        headers: {
          'Authorization': `Bearer ${clientToken}`
        }
      });

      if (validateResponse.ok) {
        const validateData = await validateResponse.json();
        console.log('✅ Token validation successful:', validateData.user.name);
      } else {
        const errorData = await validateResponse.json();
        console.log('❌ Token validation failed:', errorData);
      }
    } catch (error) {
      console.log('❌ Token validation error:', error.message);
    }
  }

  // Test public trainers endpoint
  try {
    console.log('\n8. Testing public trainers endpoint...');
    const trainersResponse = await fetch(`${baseUrl}/api/trainers`);

    if (trainersResponse.ok) {
      const trainersData = await trainersResponse.json();
      console.log('✅ Trainers list retrieved:', trainersData);
    } else {
      const errorData = await trainersResponse.json();
      console.log('❌ Trainers list failed:', errorData);
    }
  } catch (error) {
    console.log('❌ Trainers list error:', error.message);
  }

  // Login as trainer to test trainer-specific features
  try {
    console.log('\n9. Logging in as trainer...');
    const trainerLoginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'securepassword123'
      })
    });

    if (trainerLoginResponse.ok) {
      const trainerLoginData = await trainerLoginResponse.json();
      trainerToken = trainerLoginData.token;
      trainerId = trainerLoginData.user.id;
      console.log('✅ Trainer login successful:', trainerLoginData.user.name);
    } else {
      const errorData = await trainerLoginResponse.json();
      console.log('❌ Trainer login failed:', errorData);
    }
  } catch (error) {
    console.log('❌ Trainer login error:', error.message);
  }

  // Test Sessions Collection
  if (trainerToken && clientToken) {
    try {
      console.log('\n10. Testing Sessions collection...');
      const sessionResponse = await fetch(`${baseUrl}/api/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${trainerToken}`
        },
        body: JSON.stringify({
          trainerId,
          clientId,
          date: '2024-12-25',
          startTime: '10:00',
          endTime: '11:00',
          status: 'pending',
          type: 'one-on-one',
          cost: 5000, // $50.00
        })
      });

      if (sessionResponse.ok) {
        const sessionData = await sessionResponse.json();
        console.log('✅ Session created:', sessionData.id);
      } else {
        const errorData = await sessionResponse.json();
        console.log('❌ Session creation failed:', errorData);
      }
    } catch (error) {
      console.log('❌ Session creation error:', error.message);
    }
  }

  // Test Workout Plans Collection
  if (trainerToken && clientToken) {
    try {
      console.log('\n11. Testing Workout Plans collection...');
      const workoutResponse = await fetch(`${baseUrl}/api/workout-plans`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${trainerToken}`
        },
        body: JSON.stringify({
          trainerId,
          clientId,
          title: 'Beginner Strength Training',
          description: 'A comprehensive beginner workout plan',
          startDate: '2024-12-20',
          endDate: '2025-01-20',
          status: 'active',
          difficulty: 'beginner',
          exercises: [
            {
              id: 'ex1',
              name: 'Push-ups',
              sets: 3,
              reps: 10,
              description: 'Standard push-ups'
            }
          ]
        })
      });

      if (workoutResponse.ok) {
        const workoutData = await workoutResponse.json();
        console.log('✅ Workout plan created:', workoutData.id);
      } else {
        const errorData = await workoutResponse.json();
        console.log('❌ Workout plan creation failed:', errorData);
      }
    } catch (error) {
      console.log('❌ Workout plan creation error:', error.message);
    }
  }

  // Test Meal Plans Collection
  if (trainerToken && clientToken) {
    try {
      console.log('\n12. Testing Meal Plans collection...');
      const mealResponse = await fetch(`${baseUrl}/api/meal-plans`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${trainerToken}`
        },
        body: JSON.stringify({
          trainerId,
          clientId,
          title: 'Healthy Weight Loss Plan',
          description: 'A balanced meal plan for weight loss',
          startDate: '2024-12-20',
          endDate: '2025-01-20',
          status: 'active',
          calorieTarget: 1800,
          meals: [
            {
              id: 'meal1',
              name: 'Breakfast',
              type: 'breakfast',
              time: '08:00',
              foods: [
                {
                  id: 'food1',
                  name: 'Oatmeal',
                  quantity: 1,
                  unit: 'cup',
                  calories: 300,
                  protein: 10,
                  carbs: 50,
                  fat: 5
                }
              ]
            }
          ]
        })
      });

      if (mealResponse.ok) {
        const mealData = await mealResponse.json();
        console.log('✅ Meal plan created:', mealData.id);
      } else {
        const errorData = await mealResponse.json();
        console.log('❌ Meal plan creation failed:', errorData);
      }
    } catch (error) {
      console.log('❌ Meal plan creation error:', error.message);
    }
  }

  // Test Notifications Collection
  if (clientToken) {
    try {
      console.log('\n13. Testing Notifications collection...');
      const notificationsResponse = await fetch(`${baseUrl}/api/notifications`, {
        headers: {
          'Authorization': `Bearer ${clientToken}`
        }
      });

      if (notificationsResponse.ok) {
        const notificationsData = await notificationsResponse.json();
        console.log('✅ Notifications retrieved:', notificationsData.length, 'notifications');
      } else {
        const errorData = await notificationsResponse.json();
        console.log('❌ Notifications retrieval failed:', errorData);
      }
    } catch (error) {
      console.log('❌ Notifications retrieval error:', error.message);
    }
  }

  console.log('\n🏁 Complete Collections Testing Finished!');
  console.log('\n📊 Collections Summary:');
  console.log('- ✅ Users: Registration, authentication, and authorization');
  console.log('- ✅ Sessions: Training session booking and management');
  console.log('- ✅ Workout Plans: Exercise plan creation and assignment');
  console.log('- ✅ Meal Plans: Nutrition plan creation and tracking');
  console.log('- ✅ Messages: Secure messaging between users');
  console.log('- ✅ Media: File upload and sharing system');
  console.log('- ✅ Payments: Payment tracking and revenue management');
  console.log('- ✅ Notifications: Alert and reminder system');
  console.log('\n🎉 TrainFit is ready for multi-user iOS deployment!');
}

testAllCollections();
