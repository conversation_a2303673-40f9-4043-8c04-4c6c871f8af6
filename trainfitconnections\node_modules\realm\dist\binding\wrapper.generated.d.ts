import { Decimal128, ObjectId, UUID } from "bson";
import * as utils from "./utils";
import "./patch";
export declare namespace binding {
    const enum SchemaMode {
        Automatic = 0,
        Immutable = 1,
        ReadOnly = 2,
        SoftResetFile = 3,
        HardResetFile = 4,
        AdditiveDiscovered = 5,
        AdditiveExplicit = 6,
        Manual = 7
    }
    const enum PropertyType {
        Int = 0,
        Bool = 1,
        String = 2,
        Data = 3,
        Date = 4,
        Float = 5,
        Double = 6,
        Object = 7,
        LinkingObjects = 8,
        Mixed = 9,
        ObjectId = 10,
        Decimal = 11,
        Uuid = 12,
        Required = 0,
        Nullable = 64,
        Array = 128,
        Set = 256,
        Dictionary = 512,
        Collection = 896,
        Flags = 960
    }
    const enum CollectionType {
        List = 19,
        Set = 20,
        Dictionary = 21
    }
    const enum TableType {
        TopLevel = 0,
        Embedded = 1,
        TopLevelAsymmetric = 2
    }
    const enum DataType {
        Int = 0,
        Bool = 1,
        String = 2,
        Binary = 4,
        Mixed = 6,
        Timestamp = 8,
        Float = 9,
        Double = 10,
        Decimal = 11,
        Link = 12,
        ObjectId = 15,
        TypedLink = 16,
        Uuid = 17
    }
    const enum LoggerLevel {
        All = 0,
        Trace = 1,
        Debug = 2,
        Detail = 3,
        Info = 4,
        Warn = 5,
        Error = 6,
        Fatal = 7,
        Off = 8
    }
    type AppError = Error & {
        code: number;
    };
    type CppErrorCode = Error & {
        code: number;
        category: string;
    };
    type EJson = null | string | number | boolean | EJson[] | {
        [name: string]: EJson;
    };
    export import Float = utils.Float;
    export import Status = utils.Status;
    export import ListSentinel = utils.ListSentinel;
    export import DictionarySentinel = utils.DictionarySentinel;
    class WeakRef<T extends object> {
        constructor(obj: T);
        deref(): T | undefined;
    }
    class Int64 {
        private brandForInt64;
        static add(a: Int64, b: Int64): Int64;
        static equals(a: Int64, b: Int64 | number | string): boolean;
        static isInt(a: unknown): a is Int64;
        static numToInt(a: number): Int64;
        static strToInt(a: string): Int64;
        static intToNum(a: Int64): number;
    }
    type Mixed = null | symbol | Int64 | boolean | Float | number | string | ArrayBuffer | Timestamp | Decimal128 | ObjectId | UUID | ObjLink | ObjKey;
    type MixedArg = null | Obj | Geospatial | Int64 | boolean | Float | number | string | ArrayBuffer | Timestamp | Decimal128 | ObjectId | UUID | ObjLink | ObjKey;
    /** Using an empty enum to express a nominal type */ enum Schema {
    }
    /** Using an empty enum to express a nominal type */ enum Group {
    }
    /** Using an empty enum to express a nominal type */ enum AuditInterface {
    }
    /** Using an empty enum to express a nominal type */ enum ColKey {
    }
    /** Using an empty enum to express a nominal type */ enum ObjKey {
    }
    /** Using an empty enum to express a nominal type */ enum TableKey {
    }
    type Property = {
        name: string;
        publicName: string;
        type: PropertyType;
        objectType: string;
        linkOriginPropertyName: string;
        /** @default false */
        isPrimary: boolean;
        /** @default false */
        isIndexed: boolean;
        /** @default false */
        isFulltextIndexed: boolean;
        columnKey: ColKey;
    };
    type Property_Relaxed = {
        name: string;
        publicName?: string;
        type: PropertyType;
        objectType?: string;
        linkOriginPropertyName?: string;
        /** @default false */
        isPrimary?: boolean;
        /** @default false */
        isIndexed?: boolean;
        /** @default false */
        isFulltextIndexed?: boolean;
        columnKey?: ColKey;
    };
    type VersionId = {
        /**
         * @deprecated Add 'version' to your opt-in list (under 'records/VersionID/fields/') to use this.
         * @default 0x7FFF_FFFF_FFFF_FFFF
         */
        version: Int64;
        /**
         * @deprecated Add 'index' to your opt-in list (under 'records/VersionID/fields/') to use this.
         * @default 0
         */
        index: number;
    };
    type VersionId_Relaxed = {
        /**
         * @deprecated Add 'version' to your opt-in list (under 'records/VersionID/fields/') to use this.
         * @default 0x7FFF_FFFF_FFFF_FFFF
         */
        version?: Int64;
        /**
         * @deprecated Add 'index' to your opt-in list (under 'records/VersionID/fields/') to use this.
         * @default 0
         */
        index?: number;
    };
    type ObjectSchema = {
        name: string;
        persistedProperties: Array<Property>;
        computedProperties: Array<Property>;
        primaryKey: string;
        tableKey: TableKey;
        /** @default TopLevel */
        tableType: TableType;
        /** @deprecated Add 'alias' to your opt-in list (under 'records/ObjectSchema/fields/') to use this. */
        alias: string;
    };
    type ObjectSchema_Relaxed = {
        name: string;
        persistedProperties?: Array<Property_Relaxed>;
        computedProperties?: Array<Property_Relaxed>;
        primaryKey?: string;
        tableKey?: TableKey;
        /** @default TopLevel */
        tableType?: TableType;
        /** @deprecated Add 'alias' to your opt-in list (under 'records/ObjectSchema/fields/') to use this. */
        alias?: string;
    };
    type GeoPoint = {
        longitude: number;
        latitude: number;
        /** @default std::numeric_limits<double>::quiet_NaN() */
        altitude: number;
    };
    type GeoPoint_Relaxed = {
        longitude: number;
        latitude: number;
        /** @default std::numeric_limits<double>::quiet_NaN() */
        altitude?: number;
    };
    type GeoCircle = {
        radiusRadians: number;
        center: GeoPoint;
    };
    type GeoCircle_Relaxed = {
        radiusRadians: number;
        center: GeoPoint_Relaxed;
    };
    type GeoBox = {
        lo: GeoPoint;
        hi: GeoPoint;
    };
    type GeoBox_Relaxed = {
        lo: GeoPoint_Relaxed;
        hi: GeoPoint_Relaxed;
    };
    type GeoPolygon = {
        points: Array<Array<GeoPoint>>;
    };
    type GeoPolygon_Relaxed = {
        points: Array<Array<GeoPoint_Relaxed>>;
    };
    type RealmConfig = {
        path: string;
        /** @default false */
        cache: boolean;
        encryptionKey: ArrayBuffer;
        fifoFilesFallbackPath: string;
        /** @default false */
        inMemory: boolean;
        schema: undefined | Array<ObjectSchema>;
        /** @default -1 */
        schemaVersion: Int64;
        /** @default SchemaMode::Automatic */
        schemaMode: SchemaMode;
        /** @default false */
        disableFormatUpgrade: boolean;
        migrationFunction: null | ((old_realm: Realm, new_realm: Realm) => void);
        initializationFunction: null | ((realm: Realm) => void);
        shouldCompactOnLaunchFunction: null | ((total_bytes: Int64, used_bytes: Int64) => boolean);
        /** @default false */
        automaticallyHandleBacklinksInMigrations: boolean;
    };
    type RealmConfig_Relaxed = {
        path: string;
        /** @default false */
        cache?: boolean;
        encryptionKey?: ArrayBuffer;
        fifoFilesFallbackPath?: string;
        /** @default false */
        inMemory?: boolean;
        schema?: undefined | Array<ObjectSchema_Relaxed>;
        /** @default -1 */
        schemaVersion?: Int64;
        /** @default SchemaMode::Automatic */
        schemaMode?: SchemaMode;
        /** @default false */
        disableFormatUpgrade?: boolean;
        migrationFunction?: null | ((old_realm: Realm, new_realm: Realm) => void);
        initializationFunction?: null | ((realm: Realm) => void);
        shouldCompactOnLaunchFunction?: null | ((total_bytes: Int64, used_bytes: Int64) => boolean);
        /** @default false */
        automaticallyHandleBacklinksInMigrations?: boolean;
    };
    type ObjectChangeSet = {
        isDeleted: boolean;
        changedColumns: Array<ColKey>;
    };
    type ObjectChangeSet_Relaxed = {
        isDeleted: boolean;
        changedColumns: Array<ColKey>;
    };
    type CollectionChangeSetMove = {
        /** @deprecated Add 'from' to your opt-in list (under 'records/CollectionChangeSetMove/fields/') to use this. */
        from: number;
        /** @deprecated Add 'to' to your opt-in list (under 'records/CollectionChangeSetMove/fields/') to use this. */
        to: number;
    };
    type CollectionChangeSetMove_Relaxed = {
        /** @deprecated Add 'from' to your opt-in list (under 'records/CollectionChangeSetMove/fields/') to use this. */
        from: number;
        /** @deprecated Add 'to' to your opt-in list (under 'records/CollectionChangeSetMove/fields/') to use this. */
        to: number;
    };
    type CollectionChangeSet = {
        deletions: IndexSet;
        insertions: IndexSet;
        modifications: IndexSet;
        modificationsNew: IndexSet;
        /** @deprecated Add 'moves' to your opt-in list (under 'records/CollectionChangeSet/fields/') to use this. */
        moves: Array<CollectionChangeSetMove>;
        /** @deprecated Add 'collection_root_was_deleted' to your opt-in list (under 'records/CollectionChangeSet/fields/') to use this. */
        collectionRootWasDeleted: boolean;
        /** @deprecated Add 'collection_was_cleared' to your opt-in list (under 'records/CollectionChangeSet/fields/') to use this. */
        collectionWasCleared: boolean;
    };
    type CollectionChangeSet_Relaxed = {
        deletions: IndexSet;
        insertions: IndexSet;
        modifications: IndexSet;
        modificationsNew: IndexSet;
        /** @deprecated Add 'moves' to your opt-in list (under 'records/CollectionChangeSet/fields/') to use this. */
        moves: Array<CollectionChangeSetMove_Relaxed>;
        /** @deprecated Add 'collection_root_was_deleted' to your opt-in list (under 'records/CollectionChangeSet/fields/') to use this. */
        collectionRootWasDeleted: boolean;
        /** @deprecated Add 'collection_was_cleared' to your opt-in list (under 'records/CollectionChangeSet/fields/') to use this. */
        collectionWasCleared: boolean;
    };
    type DictionaryChangeSet = {
        deletions: Array<Mixed>;
        insertions: Array<Mixed>;
        modifications: Array<Mixed>;
        /** @deprecated Add 'collection_root_was_deleted' to your opt-in list (under 'records/DictionaryChangeSet/fields/') to use this. */
        collectionRootWasDeleted: boolean;
    };
    type DictionaryChangeSet_Relaxed = {
        deletions: Array<MixedArg>;
        insertions: Array<MixedArg>;
        modifications: Array<MixedArg>;
        /** @deprecated Add 'collection_root_was_deleted' to your opt-in list (under 'records/DictionaryChangeSet/fields/') to use this. */
        collectionRootWasDeleted: boolean;
    };
    type BindingContext = Record<string, never>;
    type BindingContext_Relaxed = {
        didChange?: null | ((r: Realm) => void);
        beforeNotify?: null | ((r: Realm) => void);
        schemaDidChange?: null | ((r: Realm) => void);
    };
    class Helpers {
        /** Opting out of structural typing */
        private brandForHelpers;
        private constructor();
        static getTable(r: Realm, key: TableKey): TableRef;
        static getKeypathMapping(r: Realm): KeyPathMapping;
        static resultsAppendQuery(results: Results, query: Query): Results;
        static makeObjectNotifier(r: Realm, o: Obj): ObjectNotifier;
        static setBindingContext(r: Realm, methods: BindingContext_Relaxed): void;
        static getOrCreateObjectWithPrimaryKey(t: TableRef, pk: MixedArg): [Obj, boolean];
        static deleteDataForObject(realm: Realm, object_type: string): void;
        static base64Decode(input: string): ArrayBuffer;
        static makeLoggerFactory(log: (category: Readonly<string>, level: LoggerLevel, message: Readonly<string>) => void): LoggerFactory;
        static makeLogger(log: (category: Readonly<string>, level: LoggerLevel, message: Readonly<string>) => void): Logger;
        static consumeThreadSafeReferenceToSharedRealm(tsr: ThreadSafeReference): Realm;
        static fileExists(path: string): boolean;
        static getResultsDescription(results: Readonly<Results>): string;
        static needsFileFormatUpgrade(config: Readonly<RealmConfig_Relaxed>): boolean;
    }
    class LogCategoryRef {
        /** Opting out of structural typing */
        private brandForLogCategoryRef;
        private constructor();
        setDefaultLevelThreshold(level: LoggerLevel): void;
        static getCategory(name: string): LogCategoryRef;
    }
    class Logger {
        /** Opting out of structural typing */
        private brandForLogger;
        private constructor();
        static setDefaultLogger(logger: null | Logger): void;
    }
    class ConstTableRef {
        /** Opting out of structural typing */
        private brandForConstTableRef;
        protected constructor();
        getColumnType(column: ColKey): DataType;
        getLinkTarget(column: ColKey): ConstTableRef;
        getObject(key: ObjKey): Obj;
        tryGetObject(key: ObjKey): null | Obj;
        query(query_string: string, args: Array<MixedArg | MixedArg[]>, mapping: KeyPathMapping): Query;
        findPrimaryKey(pk: MixedArg): ObjKey;
        get key(): TableKey;
        [Symbol.iterator](): Iterator<Obj>;
    }
    class TableRef extends ConstTableRef {
        /** Opting out of structural typing */
        private brandForTableRef;
        private constructor();
        createObject(): Obj;
        removeObject(key: ObjKey): void;
        getLinkTarget(column: ColKey): TableRef;
        clear(): void;
        getPrimaryKeyColumn(): ColKey;
    }
    class Obj {
        /** Opting out of structural typing */
        private brandForObj;
        private constructor();
        getAny(column: ColKey): Mixed;
        setAny(column: ColKey, value: MixedArg): void;
        setCollection(column: ColKey, type: CollectionType): Obj;
        addInt(column: ColKey, value: Int64): Obj;
        getLinkedObject(column: ColKey): null | Obj;
        getBacklinkCount(): number;
        getBacklinkView(src_table: TableRef, src_col_key: ColKey): TableView;
        createAndSetLinkedObject(column: ColKey): Obj;
        get isValid(): boolean;
        get table(): TableRef;
        get key(): ObjKey;
    }
    class Transaction {
        /** Opting out of structural typing */
        private brandForTransaction;
        private constructor();
    }
    class ObjectStore {
        /** Opting out of structural typing */
        private brandForObjectStore;
        private constructor();
    }
    class Timestamp {
        /** Opting out of structural typing */
        private brandForTimestamp;
        private constructor();
        static make(seconds: Int64, nanoseconds: number): Timestamp;
        get seconds(): Int64;
        get nanoseconds(): number;
    }
    class Geospatial {
        /** Opting out of structural typing */
        private brandForGeospatial;
        private constructor();
        static makeFromCircle(circle: GeoCircle_Relaxed): Geospatial;
        static makeFromBox(box: GeoBox_Relaxed): Geospatial;
        static makeFromPolygon(polygon: GeoPolygon_Relaxed): Geospatial;
    }
    class ObjLink {
        /** Opting out of structural typing */
        private brandForObjLink;
        private constructor();
        get tableKey(): TableKey;
        get objKey(): ObjKey;
    }
    class KeyPathMapping {
        /** Opting out of structural typing */
        private brandForKeyPathMapping;
        private constructor();
    }
    class Query {
        /** Opting out of structural typing */
        private brandForQuery;
        private constructor();
        get table(): ConstTableRef;
        get description(): string;
    }
    class SortDescriptor {
        /** Opting out of structural typing */
        private brandForSortDescriptor;
        private constructor();
    }
    class TableView {
        /** Opting out of structural typing */
        private brandForTableView;
        private constructor();
    }
    class Results {
        /** Opting out of structural typing */
        private brandForResults;
        private constructor();
        size(): number;
        indexOf(value: MixedArg): number;
        indexOfObj(obj: Obj): number;
        getObj(index: number): Obj;
        getAny(index: number): Mixed;
        getList(index: number): List;
        getDictionary(index: number): Dictionary;
        sortByNames(order: Array<[string, boolean]>): Results;
        snapshot(): Results;
        max(column: ColKey): undefined | Mixed;
        min(column: ColKey): undefined | Mixed;
        average(column: ColKey): undefined | Mixed;
        sum(column: ColKey): undefined | Mixed;
        clear(): void;
        addNotificationCallback(cb: (changes: Readonly<CollectionChangeSet>) => void, keyPaths: undefined | Array<Array<[TableKey, ColKey]>>): NotificationToken;
        static fromTable(r: Realm, table: ConstTableRef): Results;
        static fromTableView(r: Realm, table: TableView): Results;
        get isValid(): boolean;
        get query(): Query;
        get objectType(): string;
        get type(): PropertyType;
    }
    class Realm {
        /** Opting out of structural typing */
        private brandForRealm;
        private constructor();
        beginTransaction(): void;
        commitTransaction(): void;
        cancelTransaction(): void;
        updateSchema(schema: Array<ObjectSchema_Relaxed>, version: Int64, migration_function: null | ((old_realm: Realm, new_realm: Realm) => void), initialization_function: null | ((realm: Realm) => void), in_transaction: boolean): void;
        compact(): boolean;
        convert(config: RealmConfig_Relaxed): void;
        verifyOpen(): void;
        createKeyPathArray(table_name: string, key_paths: Readonly<Array<string>>): Array<Array<[TableKey, ColKey]>>;
        close(): void;
        static getSharedRealm(config: RealmConfig_Relaxed): Realm;
        static getSchemaVersion(config: Readonly<RealmConfig_Relaxed>): Int64;
        get config(): Readonly<RealmConfig>;
        get schema(): Readonly<Array<ObjectSchema>>;
        get schemaVersion(): Int64;
        get isInTransaction(): boolean;
        get isInMigration(): boolean;
        get isEmpty(): boolean;
        get isClosed(): boolean;
        get $addr(): number;
        $resetSharedPtr(): void;
    }
    class RealmCoordinator {
        /** Opting out of structural typing */
        private brandForRealmCoordinator;
        private constructor();
        static clearAllCaches(): void;
    }
    class ObjectNotifier {
        /** Opting out of structural typing */
        private brandForObjectNotifier;
        private constructor();
        addCallback(cb: (changes: ObjectChangeSet) => void, keyPaths: undefined | Array<Array<[TableKey, ColKey]>>): Int64;
    }
    class NotificationToken {
        /** Opting out of structural typing */
        private brandForNotificationToken;
        private constructor();
        unregister(): void;
        static forObject(notifier: ObjectNotifier, token: Int64): NotificationToken;
    }
    class IndexSet {
        /** Opting out of structural typing */
        private brandForIndexSet;
        private constructor();
        [Symbol.iterator](): Iterator<[number, number]>;
    }
    class Collection {
        /** Opting out of structural typing */
        private brandForCollection;
        protected constructor();
        getAny(ndx: number): Mixed;
        asResults(): Results;
        snapshot(): Results;
        get type(): PropertyType;
        get objectSchema(): Readonly<ObjectSchema>;
        get size(): number;
        get isValid(): boolean;
    }
    class List extends Collection {
        /** Opting out of structural typing */
        private brandForList;
        private constructor();
        getObj(ndx: number): Obj;
        getList(path_elem: number): List;
        getDictionary(path_elem: number): Dictionary;
        move(source_ndx: number, dest_ndx: number): void;
        remove(ndx: number): void;
        removeAll(): void;
        swap(ndx1: number, ndx2: number): void;
        deleteAll(): void;
        insertAny(list_ndx: number, value: MixedArg): void;
        insertEmbedded(ndx: number): Obj;
        insertCollection(path_elem: number, dict_or_list: CollectionType): void;
        setAny(list_ndx: number, value: MixedArg): void;
        setEmbedded(list_ndx: number): Obj;
        setCollection(path_element: number, dict_or_list: CollectionType): void;
        static make(r: Realm, parent: Readonly<Obj>, col: ColKey): List;
    }
    class Set extends Collection {
        /** Opting out of structural typing */
        private brandForSet;
        private constructor();
        getObj(ndx: number): Obj;
        insertAny(val: MixedArg): [number, boolean];
        removeAny(val: MixedArg): [number, boolean];
        removeAll(): void;
        deleteAll(): void;
        static make(r: Realm, parent: Readonly<Obj>, col: ColKey): Set;
    }
    class Dictionary extends Collection {
        /** Opting out of structural typing */
        private brandForDictionary;
        private constructor();
        getList(path_elem: string): List;
        getDictionary(path_elem: string): Dictionary;
        contains(key: string): boolean;
        addKeyBasedNotificationCallback(cb: (changes: DictionaryChangeSet) => void, keyPaths: undefined | Array<Array<[TableKey, ColKey]>>): NotificationToken;
        insertAny(key: string, value: MixedArg): [number, boolean];
        insertEmbedded(key: string): Obj;
        insertCollection(path_elem: string, dict_or_list: CollectionType): void;
        tryGetAny(key: string): undefined | Mixed;
        removeAll(): void;
        tryErase(key: string): boolean;
        static make(r: Realm, parent: Readonly<Obj>, col: ColKey): Dictionary;
        get keys(): Results;
        get values(): Results;
        [Symbol.iterator](): Iterator<[Readonly<Mixed>, Mixed]>;
    }
    class LoggerFactory {
        /** Opting out of structural typing */
        private brandForLoggerFactory;
        private constructor();
    }
    class ThreadSafeReference {
        /** Opting out of structural typing */
        private brandForThreadSafeReference;
        private constructor();
    }
    class Scheduler {
        /** Opting out of structural typing */
        private brandForScheduler;
        private constructor();
    }
    class JsPlatformHelpers {
        /** Opting out of structural typing */
        private brandForJsPlatformHelpers;
        private constructor();
        static setDefaultRealmFileDirectory(dir: string): void;
        static defaultRealmFileDirectory(): string;
        static ensureDirectoryExistsForFile(file: Readonly<string>): void;
        static copyBundledRealmFiles(): void;
        static removeRealmFilesFromDirectory(directory: Readonly<string>): void;
        static removeFile(path: Readonly<string>): void;
        static removeDirectory(path: Readonly<string>): void;
        static excludeFromIcloudBackup(path: Readonly<string>, value: boolean): void;
        static getCpuArch(): string;
    }
}
/**
 * Is true when the native module has been injected.
 * Useful to perform asserts on platforms which inject the native module synchronously.
 */
export declare let isReady: boolean;
/**
 * Resolves when the native module has been injected.
 * Useful to perform asserts on platforms which inject the native module asynchronously.
 */
export declare const ready: Promise<void>;
type Extras = {
    Int64: typeof binding.Int64;
    WeakRef: typeof binding.WeakRef;
};
export declare function injectNativeModule(nativeModule: any, extras: Extras): void;
export {};
