import { Realm as RealmConstructor } from "./Realm";
import * as ns from "./namespace";
declare global {
    /** @deprecated Will be removed in v13.0.0. Please use an import statement. */
    export class Realm extends RealmConstructor {
    }
    /**
     * @deprecated Will be removed in v13.0.0. Please use an import statement.
     */
    export namespace Realm {
        export import Realm = RealmConstructor;
        export import flags = ns.flags;
        export import Object = ns.RealmObject;
        export import BSON = ns.BSON;
        export import Types = ns.Types;
        export import index = ns.index;
        export import mapTo = ns.mapTo;
        export import kmToRadians = ns.kmToRadians;
        export import miToRadians = ns.miToRadians;
        export import AnyCollection = ns.AnyCollection;
        export import AnyDictionary = ns.AnyDictionary;
        export import AnyList = ns.AnyList;
        export import AnyRealmObject = ns.AnyRealmObject;
        export import AnyResults = ns.AnyResults;
        export import AssertionError = ns.AssertionError;
        export import BaseObjectSchema = ns.BaseObjectSchema;
        export import CanonicalGeoPoint = ns.CanonicalGeoPoint;
        export import CanonicalGeoPolygon = ns.CanonicalGeoPolygon;
        export import CanonicalObjectSchema = ns.CanonicalObjectSchema;
        export import CanonicalPropertiesTypes = ns.CanonicalPropertiesTypes;
        export import CanonicalPropertySchema = ns.CanonicalPropertySchema;
        export import Collection = ns.Collection;
        export import CollectionChangeCallback = ns.CollectionChangeCallback;
        export import CollectionChangeSet = ns.CollectionChangeSet;
        export import CollectionPropertyTypeName = ns.CollectionPropertyTypeName;
        export import Configuration = ns.Configuration;
        export import Dictionary = ns.Dictionary;
        export import DictionaryChangeCallback = ns.DictionaryChangeCallback;
        export import DictionaryChangeSet = ns.DictionaryChangeSet;
        export import GeoBox = ns.GeoBox;
        export import GeoCircle = ns.GeoCircle;
        export import GeoPoint = ns.GeoPoint;
        export import GeoPolygon = ns.GeoPolygon;
        export import GeoPosition = ns.GeoPosition;
        export import IndexDecorator = ns.IndexDecorator;
        export import IndexedType = ns.IndexedType;
        export import List = ns.List;
        export import Logger = ns.Logger;
        export import LogLevel = ns.LogLevel;
        export import LoggerCallback = ns.LoggerCallback;
        export import MapToDecorator = ns.MapToDecorator;
        export import MigrationCallback = ns.MigrationCallback;
        export import MigrationOptions = ns.MigrationOptions;
        export import Mixed = ns.Types.Mixed;
        export import NumericLogLevel = ns.NumericLogLevel;
        export import ObjectChangeCallback = ns.ObjectChangeCallback;
        export import ObjectChangeSet = ns.ObjectChangeSet;
        export import ObjectSchema = ns.ObjectSchema;
        export import ObjectType = ns.ObjectType;
        export import OrderedCollection = ns.OrderedCollection;
        export import PrimaryKey = ns.PrimaryKey;
        export import PrimitivePropertyTypeName = ns.PrimitivePropertyTypeName;
        export import ProgressRealmPromise = ns.ProgressRealmPromise;
        export import PropertiesTypes = ns.PropertiesTypes;
        export import PropertySchema = ns.PropertySchema;
        export import PropertySchemaParseError = ns.PropertySchemaParseError;
        export import PropertySchemaShorthand = ns.PropertySchemaShorthand;
        export import PropertySchemaStrict = ns.PropertySchemaStrict;
        export import PropertyTypeName = ns.PropertyTypeName;
        export import RealmEvent = ns.RealmEvent;
        export import RealmEventName = ns.RealmEventName;
        export import RealmListenerCallback = ns.RealmListenerCallback;
        export import RealmObjectConstructor = ns.RealmObjectConstructor;
        export import RelationshipPropertyTypeName = ns.RelationshipPropertyTypeName;
        export import Results = ns.Results;
        export import SchemaParseError = ns.SchemaParseError;
        export import Set = ns.RealmSet;
        export import SortDescriptor = ns.SortDescriptor;
        export import TypeAssertionError = ns.TypeAssertionError;
        export import Unmanaged = ns.Unmanaged;
        export import UpdateMode = ns.UpdateMode;
        /** @deprecated Will be removed in v13.0.0. Please use {@link ns.CanonicalPropertySchema | CanonicalPropertySchema} */
        export import CanonicalObjectSchemaProperty = ns.CanonicalPropertySchema;
        /** @deprecated Will be removed in v13.0.0. Please use {@link ns.PropertySchema | PropertySchema} */
        export import ObjectSchemaProperty = ns.PropertySchema;
        /** @deprecated Will be removed in v13.0.0. Please use {@link ns.RealmObjectConstructor | RealmObjectConstructor} */
        export import ObjectClass = ns.RealmObjectConstructor;
        /** @deprecated Will be removed in v13.0.0. Please use {@link ns.PropertyTypeName | PropertyTypeName} */
        export import PropertyType = ns.PropertyTypeName;
    }
}
