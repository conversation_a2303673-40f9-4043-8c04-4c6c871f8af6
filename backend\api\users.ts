import { createUser, getUserById, updateUserById, deleteUserById } from '../lib/user-crud';

export default async function handler(req, res) {
  const { method } = req;

  switch (method) {
    case 'GET': {
      // Get user by id: /api/users?id=123
      const { id } = req.query;
      if (!id || typeof id !== 'string') return res.status(400).json({ error: 'Missing id' });
      const user = await getUserById(id);
      if (!user) return res.status(404).json({ error: 'User not found' });
      return res.status(200).json(user);
    }
    case 'POST': {
      // Create user: POST body = user object
      const user = req.body;
      if (!user || !user.id) return res.status(400).json({ error: 'Missing user data' });
      const insertedId = await createUser(user);
      return res.status(201).json({ insertedId });
    }
    case 'PUT': {
      // Update user: PUT body = { id, ...update }
      const { id, ...update } = req.body;
      if (!id) return res.status(400).json({ error: 'Missing id' });
      const result = await updateUserById(id, update);
      return res.status(200).json({ modifiedCount: result.modifiedCount });
    }
    case 'DELETE': {
      // Delete user: DELETE body = { id }
      const { id } = req.body;
      if (!id) return res.status(400).json({ error: 'Missing id' });
      const result = await deleteUserById(id);
      return res.status(200).json({ deletedCount: result.deletedCount });
    }
    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      return res.status(405).end(`Method ${method} Not Allowed`);
  }
}
