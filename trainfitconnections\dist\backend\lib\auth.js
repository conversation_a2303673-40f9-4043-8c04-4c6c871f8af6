"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hashPassword = hashPassword;
exports.verifyPassword = verifyPassword;
exports.generateToken = generateToken;
exports.verifyToken = verifyToken;
exports.registerUser = registerUser;
exports.loginUser = loginUser;
exports.getUserById = getUserById;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = '7d';
const SALT_ROUNDS = 12;
// Hash password
async function hashPassword(password) {
    return bcryptjs_1.default.hash(password, SALT_ROUNDS);
}
// Verify password
async function verifyPassword(password, hash) {
    return bcryptjs_1.default.compare(password, hash);
}
// Generate JWT token
function generateToken(user) {
    const payload = {
        userId: user.id,
        email: user.email,
        role: user.role,
    };
    return jsonwebtoken_1.default.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}
// Verify JWT token
function verifyToken(token) {
    try {
        return jsonwebtoken_1.default.verify(token, JWT_SECRET);
    }
    catch (error) {
        console.error('Token verification failed:', error);
        return null;
    }
}
// Register new user
async function registerUser(email, password, name, role) {
    const client = await mongodb_1.default;
    const db = client.db('trainfitconnections');
    // Check if user already exists
    const existingUser = await db.collection('users').findOne({ email: email.toLowerCase() });
    if (existingUser) {
        throw new Error('User with this email already exists');
    }
    // Hash password
    const passwordHash = await hashPassword(password);
    // Create user
    const userId = new mongodb_2.ObjectId().toString();
    const user = {
        id: userId,
        email: email.toLowerCase(),
        name,
        role,
        passwordHash,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    await db.collection('users').insertOne(user);
    // Generate token
    const token = generateToken(user);
    // Remove password hash from returned user
    const { passwordHash: _, ...userWithoutPassword } = user;
    return { user: userWithoutPassword, token };
}
// Login user
async function loginUser(email, password) {
    const client = await mongodb_1.default;
    const db = client.db('trainfitconnections');
    // Find user
    const user = await db.collection('users').findOne({ email: email.toLowerCase() });
    if (!user) {
        throw new Error('Invalid email or password');
    }
    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash);
    if (!isValidPassword) {
        throw new Error('Invalid email or password');
    }
    // Generate token
    const token = generateToken(user);
    // Remove password hash from returned user
    const { passwordHash: _, ...userWithoutPassword } = user;
    return { user: userWithoutPassword, token };
}
// Get user by ID
async function getUserById(userId) {
    const client = await mongodb_1.default;
    const db = client.db('trainfitconnections');
    const user = await db.collection('users').findOne({ id: userId });
    if (user) {
        const { passwordHash: _, ...userWithoutPassword } = user;
        return userWithoutPassword;
    }
    return null;
}
//# sourceMappingURL=auth.js.map