import { ObjectId } from 'mongodb';
export interface MediaFile {
    _id?: ObjectId;
    id: string;
    ownerId: string;
    fileName: string;
    originalName: string;
    mimeType: string;
    fileSize: number;
    url: string;
    thumbnailUrl?: string;
    type: 'image' | 'video' | 'audio' | 'document';
    category: 'profile_photo' | 'progress_photo' | 'workout_video' | 'meal_photo' | 'document' | 'other';
    description?: string;
    tags?: string[];
    isPublic: boolean;
    sharedWith?: string[];
    metadata?: {
        width?: number;
        height?: number;
        duration?: number;
        location?: {
            latitude: number;
            longitude: number;
        };
        capturedAt?: Date;
    };
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    isDeleted?: boolean;
}
export interface ProgressPhoto {
    _id?: ObjectId;
    id: string;
    clientId: string;
    trainerId?: string;
    mediaFileId: string;
    date: string;
    weight?: number;
    bodyFatPercentage?: number;
    measurements?: {
        chest?: number;
        waist?: number;
        hips?: number;
        arms?: number;
        thighs?: number;
        neck?: number;
    };
    notes?: string;
    isVisible: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare function createMediaFile(fileData: Omit<MediaFile, '_id' | 'id' | 'createdAt' | 'updatedAt'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    ownerId: string;
    fileName: string;
    originalName: string;
    mimeType: string;
    fileSize: number;
    url: string;
    thumbnailUrl?: string;
    type: "image" | "video" | "audio" | "document";
    category: "profile_photo" | "progress_photo" | "workout_video" | "meal_photo" | "document" | "other";
    description?: string;
    tags?: string[];
    isPublic: boolean;
    sharedWith?: string[];
    metadata?: {
        width?: number;
        height?: number;
        duration?: number;
        location?: {
            latitude: number;
            longitude: number;
        };
        capturedAt?: Date;
    };
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    isDeleted?: boolean;
}>;
export declare function getMediaFilesForUser(userId: string, requestingUserId: string, filters?: {
    type?: string;
    category?: string;
    isPublic?: boolean;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getMediaFileById(fileId: string, requestingUserId: string): Promise<MediaFile>;
export declare function updateMediaFile(fileId: string, updates: Partial<MediaFile>, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteMediaFile(fileId: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function shareMediaFile(fileId: string, userIds: string[], requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function createProgressPhoto(photoData: Omit<ProgressPhoto, '_id' | 'id' | 'createdAt' | 'updatedAt'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    clientId: string;
    trainerId?: string;
    mediaFileId: string;
    date: string;
    weight?: number;
    bodyFatPercentage?: number;
    measurements?: {
        chest?: number;
        waist?: number;
        hips?: number;
        arms?: number;
        thighs?: number;
        neck?: number;
    };
    notes?: string;
    isVisible: boolean;
    createdAt: Date;
    updatedAt: Date;
}>;
export declare function getProgressPhotosForClient(clientId: string, requestingUserId: string, filters?: {
    dateFrom?: string;
    dateTo?: string;
    isVisible?: boolean;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function updateProgressPhoto(photoId: string, updates: Partial<ProgressPhoto>, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteProgressPhoto(photoId: string, requestingUserId: string): Promise<import("mongodb").DeleteResult>;
//# sourceMappingURL=media-crud.d.ts.map