import { Context, Next } from 'hono';
export interface AuthContext {
    user: {
        id: string;
        email: string;
        name: string;
        role: string;
    };
}
export declare function authMiddleware(c: Context, next: Next): Promise<(Response & import("hono").TypedResponse<{
    error: string;
}, 401, "json">) | undefined>;
export declare function optionalAuthMiddleware(c: Context, next: Next): Promise<void>;
export declare function requireRole(allowedRoles: string[]): (c: Context, next: Next) => Promise<(Response & import("hono").TypedResponse<{
    error: string;
}, 401, "json">) | (Response & import("hono").TypedResponse<{
    error: string;
}, 403, "json">) | undefined>;
export declare function getCurrentUser(c: Context): AuthContext['user'] | null;
//# sourceMappingURL=auth.d.ts.map