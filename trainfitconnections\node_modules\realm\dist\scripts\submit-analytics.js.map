{"version": 3, "file": "submit-analytics.js", "sourceRoot": "", "sources": ["../../src/scripts/submit-analytics.ts"], "names": [], "mappings": ";AAAA,4EAA4E;AAC5E,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,EAAE;AACF,4EAA4E;;;;;;AAE5E,wCAAwC;AACxC,EAAE;AACF,8EAA8E;AAC9E,6EAA6E;AAC7E,4EAA4E;AAC5E,8EAA8E;AAC9E,6EAA6E;AAC7E,6EAA6E;AAC7E,gFAAgF;AAChF,2EAA2E;AAC3E,kFAAkF;AAClF,sEAAsE;AACtE,wEAAwE;AACxE,qEAAqE;AACrE,0EAA0E;AAC1E,sDAAsD;AACtD,EAAE;AACF,mDAAmD;AACnD,8CAA8C;AAC9C,qDAAqD;AACrD,6BAA6B;AAC7B,sEAAsE;AACtE,6EAA6E;AAC7E,8BAA8B;AAE9B,sDAAyB;AACzB,0DAA6B;AAC7B,gEAAmC;AACnC,4DAA+B;AAC/B,sDAAyB;AACzB,gEAAmC;AACnC,6CAAyC;AACzC,6CAAqC;AAErC,sEAAwC;AAExC,kDAAgC;AACnB,QAAA,KAAK,GAAG,IAAA,eAAW,EAAC,wBAAwB,CAAC,CAAC;AAE3D,MAAM,gBAAgB,GAAG,mBAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC1D,MAAM,oBAAoB,GAAG,mBAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AAE/F;;GAEG;AACH,MAAM,kBAAkB,GAAG,uFAAuF,CAAC;AAEnH;;;;GAIG;AACH,MAAM,sBAAsB,GAAG,CAAC,OAAgB,EAAE,EAAE,CAClD,kBAAkB,GAAG,QAAQ,GAAG,oBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAElG;;;GAGG;AACH,SAAS,MAAM,CAAC,IAAY;IAC1B,MAAM,IAAI,GAAG,gBAAgB,CAAC;IAC9B,OAAO,IAAA,wBAAU,EAAC,QAAQ,EAAE,oBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC1F,CAAC;AAED;;;GAGG;AACH,SAAS,cAAc;IACrB,IAAI,EAAE,GAAG,sBAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC;IAC7C,IAAI,CAAC,EAAE,EAAE;QACP,EAAE,GAAG,sBAAO,CAAC,GAAG,EAAE,CAAC;QACnB,MAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACzC,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;KAC7C;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,eAAe,CAAC,WAAmB;IAC1C,MAAM,eAAe,GAAG,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IAClE,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAE,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,WAAmB,EAAE,OAAe;IAC5D,MAAM,eAAe,GAAG,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IAClE,iBAAE,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/E,CAAC;AAED;;;GAGG;AACH,SAAS,mBAAmB;IAC1B,IAAI,yBAAyB,IAAI,sBAAO,CAAC,GAAG,IAAI,IAAI,IAAI,sBAAO,CAAC,GAAG,EAAE;QACnE,OAAO,IAAI,CAAC;KACb;SAAM;IACL,oDAAoD;IACpD,sBAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY;QACxC,sBAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,MAAM,EAClC;QACA,OAAO,IAAI,CAAC;KACb;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,eAAe,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,CAAC;AACtD,CAAC;AAED;;;;GAIG;AACH,SAAS,mBAAmB;IAC1B,MAAM,oBAAoB,GAAG,mBAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;IACpF,MAAM,gBAAgB,GAAG,iBAAE;SACxB,YAAY,CAAC,oBAAoB,EAAE,MAAM,CAAC;SAC1C,KAAK,CAAC,IAAI,CAAC;SACX,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5B,MAAM,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC;IACrE,IAAI,WAAW,EAAE;QACf,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;KACvB;AACH,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,kBAA0B;IAC9C,MAAM,WAAW,GAAG,eAAe,CAAC,gBAAgB,CAAC,CAAC;IACtD,uCAAuC;IACvC,IAAI,OAAO,WAAW,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC1C,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;KACzB;IACD,WAAW,CAAC,MAAM,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC3D,gBAAgB,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;AAClD,CAAC;AAED;;;GAGG;AACH,SAAS,qBAAqB;IAC5B,MAAM,SAAS,GAAG,sBAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACvD,IAAI,SAAS,EAAE;QACb,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC3C;SAAM;QACL,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;KAC7B;AACH,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,mBAAmB,CAAC,WAAW,GAAG,cAAc,EAAE;IACtE,kGAAkG;IAClG,IAAI,UAAU,CAAC;IACf,IAAI;QACF,UAAU,GAAG,MAAM,yBAAS,CAAC,SAAS,EAAE,CAAC;KAC1C;IAAC,OAAO,GAAG,EAAE;QACZ,IAAA,aAAK,EAAC,0BAA0B,GAAG,EAAE,CAAC,CAAC;QACvC,UAAU,GAAG,iBAAE,CAAC,QAAQ,EAAE,CAAC;KAC5B;IAED,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,MAAM,gBAAgB,GAAG,mBAAmB,EAAE,CAAC;IAE/C,IAAI,SAAS,GAAG,SAAS,CAAC;IAC1B,IAAI,gBAAgB,GAAG,sBAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB;IACxE,IAAI,QAAQ,GAAG,IAAI,CAAC;IACpB,IAAI,QAAQ,GAAG,SAAS,CAAC;IAEzB,MAAM,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;IAEjD,IAAI,WAAW,CAAC,IAAI,EAAE;QACpB,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;KAChC;IAED,IAAI,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE;QACxE,SAAS,GAAG,cAAc,CAAC;QAC3B,gBAAgB,GAAG,WAAW,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;KAC7D;IACD,IAAI,WAAW,CAAC,eAAe,IAAI,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE;QAC9E,SAAS,GAAG,cAAc,CAAC;QAC3B,gBAAgB,GAAG,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;KAChE;IAED,IAAI,SAAS,KAAK,cAAc,EAAE;QAChC,IAAI;YACF,MAAM,WAAW,GAAG,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,iBAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACrD,IAAI,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBACxC,QAAQ,GAAG,QAAQ,CAAC;aACrB;iBAAM;gBACL,QAAQ,GAAG,KAAK,CAAC;aAClB;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,IAAA,aAAK,EAAC,4BAA4B,GAAG,EAAE,CAAC,CAAC;YACzC,QAAQ,GAAG,SAAS,CAAC;SACtB;QAED,IAAI;YACF,MAAM,MAAM,GAAG,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;YACzF,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAE,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;YACnE,gBAAgB,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;SAC7C;QAAC,OAAO,GAAG,EAAE;YACZ,IAAA,aAAK,EAAC,0CAA0C,GAAG,EAAE,CAAC,CAAC;SACxD;KACF;IAED,IAAI,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;QACpE,SAAS,GAAG,UAAU,CAAC;QACvB,gBAAgB,GAAG,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;KACzD;IACD,IAAI,WAAW,CAAC,eAAe,IAAI,WAAW,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;QAC1E,SAAS,GAAG,UAAU,CAAC;QACvB,gBAAgB,GAAG,WAAW,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KAC5D;IACD,IAAI,SAAS,KAAK,UAAU,EAAE;QAC5B,IAAI;YACF,MAAM,YAAY,GAAG,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAC3F,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAE,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;YAC/E,gBAAgB,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACnD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAA,aAAK,EAAC,sCAAsC,GAAG,EAAE,CAAC,CAAC;SACpD;KACF;IAED,8EAA8E;IAC9E,IAAI,QAAQ,GAAG,YAAY,CAAC;IAC5B,IAAI,eAAe,GAAG,SAAS,CAAC;IAChC,IAAI,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;QACtE,QAAQ,GAAG,YAAY,CAAC;QACxB,eAAe,GAAG,WAAW,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;KAC1D;IACD,IAAI,WAAW,CAAC,eAAe,IAAI,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE;QAC5E,QAAQ,GAAG,YAAY,CAAC;QACxB,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;KAC7D;IACD,IAAI,QAAQ,KAAK,YAAY,EAAE;QAC7B,IAAI;YACF,MAAM,cAAc,GAAG,mBAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;YAC/F,MAAM,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;YACnF,eAAe,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAC;SACpD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAA,aAAK,EAAC,wCAAwC,GAAG,EAAE,CAAC,CAAC;SACtD;KACF;IAED,MAAM,kBAAkB,GAAG,qBAAqB,EAAE,CAAC;IAEnD,OAAO;QACL,KAAK,EAAE,kCAAkC;QACzC,sBAAsB,EAAE,CAAC;QACzB,WAAW,EAAE,UAAU;QACvB,uBAAuB,EAAE,MAAM,CAAC,UAAU,CAAC;QAC3C,sBAAsB,EAAE,MAAM,CAAC,QAAQ,CAAC;QACxC,eAAe,EAAE,YAAY;QAC7B,OAAO,EAAE,YAAY;QACrB,OAAO,EAAE,WAAW,CAAC,OAAO;QAC5B,QAAQ,EAAE,QAAQ;QAClB,kBAAkB,EAAE,eAAe;QACnC,SAAS,EAAE,SAAS;QACpB,mBAAmB,EAAE,gBAAgB;QACrC,cAAc,EAAE,iBAAE,CAAC,QAAQ,EAAE;QAC7B,iBAAiB,EAAE,iBAAE,CAAC,OAAO,EAAE;QAC/B,eAAe,EAAE,iBAAE,CAAC,IAAI,EAAE;QAC1B,iBAAiB,EAAE,sBAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3C,cAAc,EAAE,gBAAgB;QAChC,cAAc,EAAE,IAAI;QACpB,qBAAqB,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAC5C,6BAA6B,EAAE,kBAAkB,CAAC,CAAC,CAAC;QACpD,gBAAgB,EAAE,QAAQ;KAC3B,CAAC;AACJ,CAAC;AAxHD,kDAwHC;AAED;;;GAGG;AACI,KAAK,UAAU,eAAe;IACnC,MAAM,IAAI,GAAG,MAAM,mBAAmB,EAAE,CAAC;IACzC,MAAM,OAAO,GAAG;QACd,KAAK,EAAE,SAAS;QAChB,UAAU,EAAE,IAAI;KACjB,CAAC;IACF,IAAA,aAAK,EAAC,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAE7C,IAAI,uBAAuB,IAAI,sBAAO,CAAC,GAAG,EAAE;QAC1C,sBAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;KAC/D;IAED,IAAI,mBAAmB,EAAE,EAAE;QACzB,IAAA,aAAK,EAAC,uBAAuB,CAAC,CAAC;QAC/B,OAAO;KACR;IAED,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;IAE3C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,kFAAkF;QAClF,gDAAgD;QAChD,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAEnD,oBAAK;aACF,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,oBAAK,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;YACzE,OAAO,CAAC;gBACN,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,aAAa,EAAE,GAAG,CAAC,aAAa;aACjC,CAAC,CAAC;QACL,CAAC,CAAC;aACD,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACrB,MAAM,OAAO,GAAG,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YAC/D,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,iCAAiC,OAAO,EAAE,CAAC,CAAC;YAClE,MAAM,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC;AArCD,0CAqCC"}