import { binding } from "./binding";
import { Collection } from "./Collection";
import type { DefaultObject } from "./schema";
import type { Realm } from "./Realm";
import type { TypeHelpers } from "./TypeHelpers";
import type { DictionaryAccessor } from "./collection-accessors/Dictionary";
declare const REALM: unique symbol;
declare const INTERNAL: unique symbol;
export type DictionaryChangeSet = {
    deletions: string[];
    modifications: string[];
    insertions: string[];
};
export type DictionaryChangeCallback<T = unknown> = (dictionary: Dictionary<T>, changes: DictionaryChangeSet) => void;
/**
 * Instances of this class are returned when accessing object properties whose type is `"Dictionary"`
 *
 * Dictionaries behave mostly like a JavaScript object i.e., as a key/value pair
 * where the key is a string.
 */
export declare class Dictionary<T = unknown> extends Collection<string, T, [
    string,
    T
], [
    string,
    T
], DictionaryChangeCallback<T>, 
/** @internal */
DictionaryAccessor<T>> {
    /** @internal */
    private [REALM];
    /**
     * The representation in the binding.
     * @internal
     */
    private readonly [INTERNAL];
    /**
     * Create a `Results` wrapping a set of query `Results` from the binding.
     * @internal
     */
    constructor(realm: Realm, internal: binding.Dictionary, accessor: DictionaryAccessor<T>, typeHelpers: TypeHelpers<T>);
    /** @ts-expect-error We're exposing methods in the end-users namespace of keys */
    [key: string]: T;
    /**
     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/entries Array.prototype.entries}
     * @returns An iterator with all entries in the dictionary.
     */
    [Symbol.iterator](): Generator<[string, T]>;
    /**
     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/keys Array.prototype.keys}
     * @returns An iterator with all values in the dictionary.
     * @since 10.5.0
     * @ts-expect-error We're exposing methods in the end-users namespace of keys */
    keys(): Generator<string>;
    /**
     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/values Array.prototype.values}
     * @returns An iterator with all values in the dictionary.
     * @since 10.5.0
     * @ts-expect-error We're exposing methods in the end-users namespace of values */
    values(): Generator<T>;
    /**
     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/entries Array.prototype.entries}
     * @returns An iterator with all key/value pairs in the dictionary.
     * @since 10.5.0
     * @ts-expect-error We're exposing methods in the end-users namespace of entries */
    entries(): Generator<[string, T]>;
    /**
     * Checks if this dictionary has not been deleted and is part of a valid Realm.
     * @returns `true` if the dictionary can be safely accessed.
     * @since 0.14.0
     * @ts-expect-error We're exposing methods in the end-users namespace of keys */
    isValid(): boolean;
    /**
     * Adds one or more elements with specified key and value to the dictionary or updates value if key exists.
     * @param elements The object of element(s) to add.
     * @throws an {@link AssertionError} If not inside a write transaction, input object contains symbol keys or if any value violates types constraints.
     * @returns The dictionary.
     * @since 10.6.0
     * @ts-expect-error We're exposing methods in the end-users namespace of keys */
    set(elements: {
        [key: string]: T;
    }): this;
    /**
     * Adds an element with the specified key and value to the dictionary or updates value if key exists.
     * @param key The key of the element to add.
     * @param value The value of the element to add.
     * @throws an {@link AssertionError} If not inside a write transaction, key is a symbol or if value violates type constraints.
     * @returns The dictionary.
     * @since 12.0.0
     */
    set(key: string, value: T): this;
    /**
     * Removes elements from the dictionary, with the keys provided.
     * This does not throw if the keys are already missing from the dictionary.
     * @param key - The key to be removed.
     * @throws An {@link AssertionError} if not inside a write transaction.
     * @returns The dictionary
     * @since 10.6.0
     * @ts-expect-error We're exposing methods in the end-users namespace of keys */
    remove(key: string | string[]): this;
    /**
     * The plain object representation for JSON serialization.
     * Use circular JSON serialization libraries such as [@ungap/structured-clone](https://www.npmjs.com/package/@ungap/structured-clone)
     * and [flatted](https://www.npmjs.com/package/flatted) to stringify Realm entities that have circular structures.
     * @returns A plain object.
     * @ts-expect-error We're exposing methods in the end-users namespace of keys */
    toJSON(_?: string, cache?: unknown): DefaultObject;
}
export type AnyDictionary = Dictionary<any>;
export {};
