"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMealPlan = createMealPlan;
exports.getMealPlansForUser = getMealPlansForUser;
exports.getMealPlanById = getMealPlanById;
exports.updateMealPlan = updateMealPlan;
exports.deleteMealPlan = deleteMealPlan;
exports.getTrainerMealPlans = getTrainerMealPlans;
exports.getClientMealPlans = getClientMealPlans;
exports.assignMealPlanToClient = assignMealPlanToClient;
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const dbName = 'trainfitconnections';
// CREATE a meal plan
async function createMealPlan(planData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only trainers can create meal plans
    if (planData.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only trainers can create meal plans');
    }
    const planId = new mongodb_2.ObjectId().toString();
    const mealPlan = {
        ...planData,
        id: planId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('meal_plans').insertOne(mealPlan);
    return { ...mealPlan, _id: result.insertedId };
}
// READ meal plans for a user
async function getMealPlansForUser(userId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own meal plans
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own meal plans');
    }
    const query = {
        $or: [
            { trainerId: userId },
            { clientId: userId }
        ]
    };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.isTemplate !== undefined) {
        query.isTemplate = filters.isTemplate;
    }
    if (filters?.dietaryRestrictions && filters.dietaryRestrictions.length > 0) {
        query.dietaryRestrictions = { $in: filters.dietaryRestrictions };
    }
    return db.collection('meal_plans').find(query).sort({ createdAt: -1 }).toArray();
}
// READ a specific meal plan
async function getMealPlanById(planId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const plan = await db.collection('meal_plans').findOne({ id: planId });
    if (!plan) {
        throw new Error('Meal plan not found');
    }
    // Verify user can access this plan
    if (plan.trainerId !== requestingUserId && plan.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only access meal plans assigned to you');
    }
    return plan;
}
// UPDATE a meal plan
async function updateMealPlan(planId, updates, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the plan exists and user has access
    const existingPlan = await getMealPlanById(planId, requestingUserId);
    // Only trainers can update meal plans
    if (existingPlan.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only the trainer who created this plan can update it');
    }
    // Remove fields that shouldn't be updated directly
    const { _id, id, createdAt, ...safeUpdates } = updates;
    const updateData = {
        ...safeUpdates,
        updatedAt: new Date(),
    };
    const result = await db.collection('meal_plans').updateOne({ id: planId }, { $set: updateData });
    return result;
}
// DELETE a meal plan
async function deleteMealPlan(planId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the plan exists and user has access
    const existingPlan = await getMealPlanById(planId, requestingUserId);
    // Only trainers can delete meal plans
    if (existingPlan.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only the trainer who created this plan can delete it');
    }
    const result = await db.collection('meal_plans').deleteOne({ id: planId });
    return result;
}
// Get meal plans created by a trainer
async function getTrainerMealPlans(trainerId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the trainer can access their meal plans
    if (trainerId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own meal plans');
    }
    const query = { trainerId };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.isTemplate !== undefined) {
        query.isTemplate = filters.isTemplate;
    }
    if (filters?.clientId) {
        query.clientId = filters.clientId;
    }
    return db.collection('meal_plans').find(query).sort({ createdAt: -1 }).toArray();
}
// Get meal plans assigned to a client
async function getClientMealPlans(clientId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the client can access their meal plans
    if (clientId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own meal plans');
    }
    const query = { clientId };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.dietaryRestrictions && filters.dietaryRestrictions.length > 0) {
        query.dietaryRestrictions = { $in: filters.dietaryRestrictions };
    }
    return db.collection('meal_plans').find(query).sort({ createdAt: -1 }).toArray();
}
// Assign a meal plan to a client (copy from template or assign existing)
async function assignMealPlanToClient(planId, clientId, requestingUserId, customizations) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Get the original plan
    const originalPlan = await getMealPlanById(planId, requestingUserId);
    // Only trainers can assign meal plans
    if (originalPlan.trainerId !== requestingUserId) {
        throw new Error('Access denied: Only trainers can assign meal plans');
    }
    // Create a new plan for the client
    const newPlanId = new mongodb_2.ObjectId().toString();
    const assignedPlan = {
        ...originalPlan,
        id: newPlanId,
        clientId,
        isTemplate: false,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        ...customizations,
    };
    // Remove the _id to create a new document
    delete assignedPlan._id;
    const result = await db.collection('meal_plans').insertOne(assignedPlan);
    return { ...assignedPlan, _id: result.insertedId };
}
//# sourceMappingURL=meal-plans-crud.js.map