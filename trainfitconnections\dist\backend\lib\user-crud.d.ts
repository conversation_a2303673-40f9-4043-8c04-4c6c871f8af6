import { ObjectId } from 'mongodb';
export declare function createUserProfile(userId: string, profileData: any): Promise<ObjectId>;
export declare function getUserById(id: string, requestingUserId: string): Promise<import("mongodb").WithId<import("bson").Document> | null>;
export declare function updateUserById(id: string, update: any, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteUserById(id: string, requestingUserId: string): Promise<import("mongodb").DeleteResult>;
export declare function getTrainers(): Promise<import("mongodb").WithId<import("bson").Document>[]>;
//# sourceMappingURL=user-crud.d.ts.map