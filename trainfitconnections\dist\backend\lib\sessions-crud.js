"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSession = createSession;
exports.getSessionsForUser = getSessionsForUser;
exports.getSessionById = getSessionById;
exports.updateSession = updateSession;
exports.deleteSession = deleteSession;
exports.getTrainerSessions = getTrainerSessions;
exports.getClientSessions = getClientSessions;
exports.updateSessionStatus = updateSessionStatus;
const mongodb_1 = __importDefault(require("./mongodb"));
const mongodb_2 = require("mongodb");
const dbName = 'trainfitconnections';
// CREATE a session
async function createSession(sessionData, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Verify user can create this session (must be trainer or client involved)
    if (sessionData.trainerId !== requestingUserId && sessionData.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only create sessions you are involved in');
    }
    const sessionId = new mongodb_2.ObjectId().toString();
    const session = {
        ...sessionData,
        id: sessionId,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const result = await db.collection('sessions').insertOne(session);
    return { ...session, _id: result.insertedId };
}
// READ sessions for a user (trainer or client)
async function getSessionsForUser(userId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Users can only access their own sessions
    if (userId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own sessions');
    }
    const query = {
        $or: [
            { trainerId: userId },
            { clientId: userId }
        ]
    };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.dateFrom || filters?.dateTo) {
        query.date = {};
        if (filters.dateFrom) {
            query.date.$gte = filters.dateFrom;
        }
        if (filters.dateTo) {
            query.date.$lte = filters.dateTo;
        }
    }
    if (filters?.type) {
        query.type = filters.type;
    }
    return db.collection('sessions').find(query).sort({ date: 1, startTime: 1 }).toArray();
}
// READ a specific session
async function getSessionById(sessionId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    const session = await db.collection('sessions').findOne({ id: sessionId });
    if (!session) {
        throw new Error('Session not found');
    }
    // Verify user can access this session
    if (session.trainerId !== requestingUserId && session.clientId !== requestingUserId) {
        throw new Error('Access denied: You can only access sessions you are involved in');
    }
    return session;
}
// UPDATE a session
async function updateSession(sessionId, updates, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the session exists and user has access
    const existingSession = await getSessionById(sessionId, requestingUserId);
    // Remove fields that shouldn't be updated directly
    const { _id, id, createdAt, ...safeUpdates } = updates;
    const updateData = {
        ...safeUpdates,
        updatedAt: new Date(),
    };
    const result = await db.collection('sessions').updateOne({ id: sessionId }, { $set: updateData });
    return result;
}
// DELETE a session
async function deleteSession(sessionId, requestingUserId) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // First verify the session exists and user has access
    await getSessionById(sessionId, requestingUserId);
    const result = await db.collection('sessions').deleteOne({ id: sessionId });
    return result;
}
// Get sessions for a trainer (trainer-specific view)
async function getTrainerSessions(trainerId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the trainer can access their sessions
    if (trainerId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own sessions');
    }
    const query = { trainerId };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.dateFrom || filters?.dateTo) {
        query.date = {};
        if (filters.dateFrom) {
            query.date.$gte = filters.dateFrom;
        }
        if (filters.dateTo) {
            query.date.$lte = filters.dateTo;
        }
    }
    return db.collection('sessions').find(query).sort({ date: 1, startTime: 1 }).toArray();
}
// Get sessions for a client (client-specific view)
async function getClientSessions(clientId, requestingUserId, filters) {
    const client = await mongodb_1.default;
    const db = client.db(dbName);
    // Only the client can access their sessions
    if (clientId !== requestingUserId) {
        throw new Error('Access denied: You can only access your own sessions');
    }
    const query = { clientId };
    // Apply filters
    if (filters?.status) {
        query.status = filters.status;
    }
    if (filters?.dateFrom || filters?.dateTo) {
        query.date = {};
        if (filters.dateFrom) {
            query.date.$gte = filters.dateFrom;
        }
        if (filters.dateTo) {
            query.date.$lte = filters.dateTo;
        }
    }
    return db.collection('sessions').find(query).sort({ date: 1, startTime: 1 }).toArray();
}
// Update session status (for booking flow)
async function updateSessionStatus(sessionId, status, requestingUserId, additionalData) {
    const updates = {
        status,
        ...additionalData,
    };
    return updateSession(sessionId, updates, requestingUserId);
}
//# sourceMappingURL=sessions-crud.js.map