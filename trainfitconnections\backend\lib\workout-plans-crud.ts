import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

export interface Exercise {
  id: string;
  name: string;
  description?: string;
  sets: number;
  reps: number | string; // Can be "10-12" or just "10"
  weight?: number;
  duration?: string; // For time-based exercises like "30 seconds"
  restTime?: string; // Rest between sets
  notes?: string;
  videoUrl?: string;
  imageUrl?: string;
  targetMuscles?: string[];
  equipment?: string[];
}

export interface WorkoutPlan {
  _id?: ObjectId;
  id: string;
  trainerId: string;
  clientId: string;
  title: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  exercises: Exercise[];
  notes?: string;
  status: 'active' | 'completed' | 'archived';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration?: number; // in minutes
  category?: string; // e.g., "strength", "cardio", "flexibility"
  tags?: string[];
  isTemplate?: boolean; // If true, can be reused for multiple clients
}

// CREATE a workout plan
export async function createWorkoutPlan(
  planData: Omit<WorkoutPlan, '_id' | 'id' | 'createdAt' | 'updatedAt'>, 
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only trainers can create workout plans
  if (planData.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only trainers can create workout plans');
  }
  
  const planId = new ObjectId().toString();
  const workoutPlan: WorkoutPlan = {
    ...planData,
    id: planId,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  const result = await db.collection('workout_plans').insertOne(workoutPlan);
  return { ...workoutPlan, _id: result.insertedId };
}

// READ workout plans for a user
export async function getWorkoutPlansForUser(userId: string, requestingUserId: string, filters?: {
  status?: string;
  difficulty?: string;
  category?: string;
  isTemplate?: boolean;
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only access their own workout plans
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own workout plans');
  }
  
  const query: any = {
    $or: [
      { trainerId: userId },
      { clientId: userId }
    ]
  };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.difficulty) {
    query.difficulty = filters.difficulty;
  }
  
  if (filters?.category) {
    query.category = filters.category;
  }
  
  if (filters?.isTemplate !== undefined) {
    query.isTemplate = filters.isTemplate;
  }
  
  return db.collection('workout_plans').find(query).sort({ createdAt: -1 }).toArray();
}

// READ a specific workout plan
export async function getWorkoutPlanById(planId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const plan = await db.collection('workout_plans').findOne({ id: planId }) as WorkoutPlan | null;
  
  if (!plan) {
    throw new Error('Workout plan not found');
  }
  
  // Verify user can access this plan
  if (plan.trainerId !== requestingUserId && plan.clientId !== requestingUserId) {
    throw new Error('Access denied: You can only access workout plans assigned to you');
  }
  
  return plan;
}

// UPDATE a workout plan
export async function updateWorkoutPlan(planId: string, updates: Partial<WorkoutPlan>, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the plan exists and user has access
  const existingPlan = await getWorkoutPlanById(planId, requestingUserId);
  
  // Only trainers can update workout plans
  if (existingPlan.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only the trainer who created this plan can update it');
  }
  
  // Remove fields that shouldn't be updated directly
  const { _id, id, createdAt, ...safeUpdates } = updates;
  
  const updateData = {
    ...safeUpdates,
    updatedAt: new Date(),
  };
  
  const result = await db.collection('workout_plans').updateOne(
    { id: planId },
    { $set: updateData }
  );
  
  return result;
}

// DELETE a workout plan
export async function deleteWorkoutPlan(planId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify the plan exists and user has access
  const existingPlan = await getWorkoutPlanById(planId, requestingUserId);
  
  // Only trainers can delete workout plans
  if (existingPlan.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only the trainer who created this plan can delete it');
  }
  
  const result = await db.collection('workout_plans').deleteOne({ id: planId });
  return result;
}

// Get workout plans created by a trainer
export async function getTrainerWorkoutPlans(trainerId: string, requestingUserId: string, filters?: {
  status?: string;
  isTemplate?: boolean;
  clientId?: string;
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the trainer can access their workout plans
  if (trainerId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own workout plans');
  }
  
  const query: any = { trainerId };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.isTemplate !== undefined) {
    query.isTemplate = filters.isTemplate;
  }
  
  if (filters?.clientId) {
    query.clientId = filters.clientId;
  }
  
  return db.collection('workout_plans').find(query).sort({ createdAt: -1 }).toArray();
}

// Get workout plans assigned to a client
export async function getClientWorkoutPlans(clientId: string, requestingUserId: string, filters?: {
  status?: string;
  difficulty?: string;
}) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Only the client can access their workout plans
  if (clientId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own workout plans');
  }
  
  const query: any = { clientId };
  
  // Apply filters
  if (filters?.status) {
    query.status = filters.status;
  }
  
  if (filters?.difficulty) {
    query.difficulty = filters.difficulty;
  }
  
  return db.collection('workout_plans').find(query).sort({ createdAt: -1 }).toArray();
}

// Assign a workout plan to a client (copy from template or assign existing)
export async function assignWorkoutPlanToClient(
  planId: string, 
  clientId: string, 
  requestingUserId: string,
  customizations?: {
    startDate?: string;
    endDate?: string;
    notes?: string;
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Get the original plan
  const originalPlan = await getWorkoutPlanById(planId, requestingUserId);
  
  // Only trainers can assign workout plans
  if (originalPlan.trainerId !== requestingUserId) {
    throw new Error('Access denied: Only trainers can assign workout plans');
  }
  
  // Create a new plan for the client
  const newPlanId = new ObjectId().toString();
  const assignedPlan: WorkoutPlan = {
    ...originalPlan,
    id: newPlanId,
    clientId,
    isTemplate: false,
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...customizations,
  };
  
  // Remove the _id to create a new document
  delete assignedPlan._id;
  
  const result = await db.collection('workout_plans').insertOne(assignedPlan);
  return { ...assignedPlan, _id: result.insertedId };
}
