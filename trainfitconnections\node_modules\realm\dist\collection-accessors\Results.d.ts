import { binding } from "../binding";
import type { TypeHelpers } from "../TypeHelpers";
/** @internal */
export type ResultsAccessor<T = unknown> = {
    get: (results: binding.Results, index: number) => T;
};
type ResultsAccessorFactoryOptions<T> = {
    realm: Realm;
    typeHelpers: TypeHelpers<T>;
    itemType: binding.PropertyType;
};
/** @internal */
export declare function createResultsAccessor<T>(options: ResultsAccessorFactoryOptions<T>): ResultsAccessor<T>;
export {};
