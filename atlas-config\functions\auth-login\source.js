// Atlas App Services Function: auth-login
// This function is called directly by the Realm SDK, not via HTTP
exports = async function(credentials) {
  try {
    const { email, password } = credentials;

    // Validate required fields
    if (!email || !password) {
      throw new Error("Missing email or password");
    }

    // Get MongoDB service
    const mongodb = context.services.get("mongodb-atlas");
    const users = mongodb.db("trainfit").collection("users");

    // Find user by Atlas user ID (more secure)
    const user = await users.findOne({ 
      userId: context.user.id 
    });
    
    if (!user) {
      throw new Error("User profile not found");
    }

    // Return user data (Atlas handles password verification)
    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      token: context.user.accessToken || 'atlas-authenticated'
    };

  } catch (error) {
    console.log("Login error:", error);
    throw new Error(error.message || "Login failed");
  }
};
