import { ObjectId } from 'mongodb';
export interface Message {
    _id?: ObjectId;
    id: string;
    senderId: string;
    receiverId: string;
    content: string;
    type: 'text' | 'image' | 'video' | 'audio' | 'file';
    mediaUrl?: string;
    fileName?: string;
    fileSize?: number;
    isRead: boolean;
    readAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    conversationId: string;
    replyToMessageId?: string;
    isDeleted?: boolean;
    deletedAt?: Date;
}
export interface Conversation {
    _id?: ObjectId;
    id: string;
    participants: string[];
    lastMessage?: {
        content: string;
        senderId: string;
        timestamp: Date;
        type: string;
    };
    lastActivity: Date;
    isArchived?: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare function createMessage(messageData: Omit<Message, '_id' | 'id' | 'createdAt' | 'updatedAt' | 'isRead'>, requestingUserId: string): Promise<{
    _id: ObjectId;
    id: string;
    senderId: string;
    receiverId: string;
    content: string;
    type: "text" | "image" | "video" | "audio" | "file";
    mediaUrl?: string;
    fileName?: string;
    fileSize?: number;
    isRead: boolean;
    readAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    conversationId: string;
    replyToMessageId?: string;
    isDeleted?: boolean;
    deletedAt?: Date;
}>;
export declare function getMessagesForConversation(conversationId: string, requestingUserId: string, options?: {
    limit?: number;
    offset?: number;
    before?: Date;
}): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function getConversationsForUser(userId: string, requestingUserId: string): Promise<import("mongodb").WithId<import("bson").Document>[]>;
export declare function markMessageAsRead(messageId: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function markConversationAsRead(conversationId: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function deleteMessage(messageId: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
export declare function getUnreadMessageCount(userId: string, requestingUserId: string): Promise<number>;
export declare function getOrCreateConversation(userId1: string, userId2: string, requestingUserId: string): Promise<Conversation>;
export declare function archiveConversation(conversationId: string, requestingUserId: string): Promise<import("mongodb").UpdateResult<import("bson").Document>>;
//# sourceMappingURL=messages-crud.d.ts.map