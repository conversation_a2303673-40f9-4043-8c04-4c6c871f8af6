import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Get the appropriate API URL based on platform
const getApiUrl = () => {
  // Check for environment variable first
  const envApiUrl = process.env.EXPO_PUBLIC_API_URL;
  if (envApiUrl) {
    return envApiUrl;
  }

  // For web, use relative URL
  if (Platform.OS === 'web') {
    return '';
  }

  // For production/TestFlight, use MongoDB Atlas App Services
  // For local development, use localhost
  return __DEV__
    ? 'http://localhost:3000'
    : 'https://data.mongodb-api.com/app/trainfit-backend-sjcdpns/endpoint';
};

const API_BASE_URL = getApiUrl();

// Token management
const TOKEN_KEY = 'auth_token';

export const tokenManager = {
  async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(TOKEN_KEY);
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  },

  async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(TOKEN_KEY, token);
    } catch (error) {
      console.error('Error setting token:', error);
    }
  },

  async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem(TOKEN_KEY);
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }
};

// API request helper with authentication
async function apiRequest<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> {
  const token = await tokenManager.getToken();
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }

  return response.json();
}

// Authentication API
export const authAPI = {
  async register(name: string, email: string, password: string, role: 'client' | 'trainer') {
    const response = await apiRequest<{
      user: any;
      token: string;
      message: string;
    }>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({ name, email, password, role }),
    });
    
    // Store the token
    await tokenManager.setToken(response.token);
    
    return response;
  },

  async login(email: string, password: string) {
    const response = await apiRequest<{
      user: any;
      token: string;
      message: string;
    }>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
    
    // Store the token
    await tokenManager.setToken(response.token);
    
    return response;
  },

  async validateToken() {
    return apiRequest<{
      user: any;
      message: string;
    }>('/api/auth/validate');
  },

  async logout() {
    await tokenManager.removeToken();
  }
};

// User API
export const userAPI = {
  async getProfile() {
    return apiRequest<any>('/api/users/me');
  },

  async updateProfile(userData: any) {
    return apiRequest<{ modifiedCount: number }>('/api/users/me', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  async deleteAccount() {
    return apiRequest<{ deletedCount: number }>('/api/users/me', {
      method: 'DELETE',
    });
  },

  async getTrainers() {
    return apiRequest<any[]>('/api/trainers');
  }
};

// Sessions API
export const sessionsAPI = {
  async createSession(sessionData: any) {
    return apiRequest<any>('/api/sessions', {
      method: 'POST',
      body: JSON.stringify(sessionData),
    });
  },

  async getSessions(filters?: any) {
    const queryParams = new URLSearchParams(filters).toString();
    return apiRequest<any[]>(`/api/sessions${queryParams ? `?${queryParams}` : ''}`);
  },

  async getSession(id: string) {
    return apiRequest<any>(`/api/sessions/${id}`);
  },

  async updateSession(id: string, updates: any) {
    return apiRequest<{ modifiedCount: number }>(`/api/sessions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  async deleteSession(id: string) {
    return apiRequest<{ deletedCount: number }>(`/api/sessions/${id}`, {
      method: 'DELETE',
    });
  }
};

// Workout Plans API
export const workoutPlansAPI = {
  async createWorkoutPlan(planData: any) {
    return apiRequest<any>('/api/workout-plans', {
      method: 'POST',
      body: JSON.stringify(planData),
    });
  },

  async getWorkoutPlans(filters?: any) {
    const queryParams = new URLSearchParams(filters).toString();
    return apiRequest<any[]>(`/api/workout-plans${queryParams ? `?${queryParams}` : ''}`);
  },

  async getWorkoutPlan(id: string) {
    return apiRequest<any>(`/api/workout-plans/${id}`);
  },

  async updateWorkoutPlan(id: string, updates: any) {
    return apiRequest<{ modifiedCount: number }>(`/api/workout-plans/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  async deleteWorkoutPlan(id: string) {
    return apiRequest<{ deletedCount: number }>(`/api/workout-plans/${id}`, {
      method: 'DELETE',
    });
  }
};

// Meal Plans API
export const mealPlansAPI = {
  async createMealPlan(planData: any) {
    return apiRequest<any>('/api/meal-plans', {
      method: 'POST',
      body: JSON.stringify(planData),
    });
  },

  async getMealPlans(filters?: any) {
    const queryParams = new URLSearchParams(filters).toString();
    return apiRequest<any[]>(`/api/meal-plans${queryParams ? `?${queryParams}` : ''}`);
  },

  async getMealPlan(id: string) {
    return apiRequest<any>(`/api/meal-plans/${id}`);
  }
};

// Messages API
export const messagesAPI = {
  async sendMessage(messageData: any) {
    return apiRequest<any>('/api/messages', {
      method: 'POST',
      body: JSON.stringify(messageData),
    });
  },

  async getConversations() {
    return apiRequest<any[]>('/api/conversations');
  },

  async getMessages(conversationId: string, options?: any) {
    const queryParams = new URLSearchParams(options).toString();
    return apiRequest<any[]>(`/api/conversations/${conversationId}/messages${queryParams ? `?${queryParams}` : ''}`);
  }
};

// Media API
export const mediaAPI = {
  async uploadMedia(fileData: any) {
    return apiRequest<any>('/api/media', {
      method: 'POST',
      body: JSON.stringify(fileData),
    });
  },

  async getMedia(filters?: any) {
    const queryParams = new URLSearchParams(filters).toString();
    return apiRequest<any[]>(`/api/media${queryParams ? `?${queryParams}` : ''}`);
  }
};

// Notifications API
export const notificationsAPI = {
  async getNotifications(filters?: any) {
    const queryParams = new URLSearchParams(filters).toString();
    return apiRequest<any[]>(`/api/notifications${queryParams ? `?${queryParams}` : ''}`);
  },

  async markAsRead(id: string) {
    return apiRequest<{ modifiedCount: number }>(`/api/notifications/${id}/read`, {
      method: 'PUT',
    });
  },

  async getUnreadCount() {
    return apiRequest<{ count: number }>('/api/notifications/unread-count');
  }
};
