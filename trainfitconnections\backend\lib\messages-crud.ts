import clientPromise from './mongodb';
import { ObjectId } from 'mongodb';

const dbName = 'trainfitconnections';

export interface Message {
  _id?: ObjectId;
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file';
  mediaUrl?: string;
  fileName?: string;
  fileSize?: number;
  isRead: boolean;
  readAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  conversationId: string; // Unique ID for the conversation between two users
  replyToMessageId?: string; // For replying to specific messages
  isDeleted?: boolean;
  deletedAt?: Date;
}

export interface Conversation {
  _id?: ObjectId;
  id: string;
  participants: string[]; // Array of user IDs
  lastMessage?: {
    content: string;
    senderId: string;
    timestamp: Date;
    type: string;
  };
  lastActivity: Date;
  isArchived?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// CREATE a message
export async function createMessage(
  messageData: Omit<Message, '_id' | 'id' | 'createdAt' | 'updatedAt' | 'isRead'>, 
  requestingUserId: string
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Verify user can send this message (must be the sender)
  if (messageData.senderId !== requestingUserId) {
    throw new Error('Access denied: You can only send messages as yourself');
  }
  
  const messageId = new ObjectId().toString();
  const message: Message = {
    ...messageData,
    id: messageId,
    isRead: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  // Insert the message
  const result = await db.collection('messages').insertOne(message);
  
  // Update or create conversation
  await updateConversation(messageData.conversationId, message);
  
  return { ...message, _id: result.insertedId };
}

// Helper function to update conversation
async function updateConversation(conversationId: string, message: Message) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const conversation = await db.collection('conversations').findOne({ id: conversationId });
  
  if (conversation) {
    // Update existing conversation
    await db.collection('conversations').updateOne(
      { id: conversationId },
      {
        $set: {
          lastMessage: {
            content: message.content,
            senderId: message.senderId,
            timestamp: message.createdAt,
            type: message.type,
          },
          lastActivity: new Date(),
          updatedAt: new Date(),
        }
      }
    );
  } else {
    // Create new conversation
    const newConversation: Conversation = {
      id: conversationId,
      participants: [message.senderId, message.receiverId],
      lastMessage: {
        content: message.content,
        senderId: message.senderId,
        timestamp: message.createdAt,
        type: message.type,
      },
      lastActivity: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    await db.collection('conversations').insertOne(newConversation);
  }
}

// READ messages for a conversation
export async function getMessagesForConversation(
  conversationId: string, 
  requestingUserId: string,
  options?: {
    limit?: number;
    offset?: number;
    before?: Date;
  }
) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify user is part of this conversation
  const conversation = await db.collection('conversations').findOne({ id: conversationId });
  if (!conversation || !conversation.participants.includes(requestingUserId)) {
    throw new Error('Access denied: You are not part of this conversation');
  }
  
  const query: any = { 
    conversationId,
    isDeleted: { $ne: true }
  };
  
  if (options?.before) {
    query.createdAt = { $lt: options.before };
  }
  
  const limit = options?.limit || 50;
  const offset = options?.offset || 0;
  
  return db.collection('messages')
    .find(query)
    .sort({ createdAt: -1 })
    .skip(offset)
    .limit(limit)
    .toArray();
}

// READ conversations for a user
export async function getConversationsForUser(userId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only access their own conversations
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only access your own conversations');
  }
  
  return db.collection('conversations')
    .find({ 
      participants: userId,
      isArchived: { $ne: true }
    })
    .sort({ lastActivity: -1 })
    .toArray();
}

// Mark message as read
export async function markMessageAsRead(messageId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const message = await db.collection('messages').findOne({ id: messageId }) as Message | null;
  
  if (!message) {
    throw new Error('Message not found');
  }
  
  // Only the receiver can mark a message as read
  if (message.receiverId !== requestingUserId) {
    throw new Error('Access denied: You can only mark messages sent to you as read');
  }
  
  const result = await db.collection('messages').updateOne(
    { id: messageId },
    {
      $set: {
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}

// Mark all messages in a conversation as read
export async function markConversationAsRead(conversationId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify user is part of this conversation
  const conversation = await db.collection('conversations').findOne({ id: conversationId });
  if (!conversation || !conversation.participants.includes(requestingUserId)) {
    throw new Error('Access denied: You are not part of this conversation');
  }
  
  const result = await db.collection('messages').updateMany(
    { 
      conversationId,
      receiverId: requestingUserId,
      isRead: false
    },
    {
      $set: {
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}

// DELETE a message (soft delete)
export async function deleteMessage(messageId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const message = await db.collection('messages').findOne({ id: messageId }) as Message | null;
  
  if (!message) {
    throw new Error('Message not found');
  }
  
  // Only the sender can delete their message
  if (message.senderId !== requestingUserId) {
    throw new Error('Access denied: You can only delete your own messages');
  }
  
  const result = await db.collection('messages').updateOne(
    { id: messageId },
    {
      $set: {
        isDeleted: true,
        deletedAt: new Date(),
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}

// Get unread message count for a user
export async function getUnreadMessageCount(userId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // Users can only check their own unread count
  if (userId !== requestingUserId) {
    throw new Error('Access denied: You can only check your own unread messages');
  }
  
  const count = await db.collection('messages').countDocuments({
    receiverId: userId,
    isRead: false,
    isDeleted: { $ne: true }
  });
  
  return count;
}

// Create or get conversation ID between two users
export async function getOrCreateConversation(userId1: string, userId2: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // User must be one of the participants
  if (requestingUserId !== userId1 && requestingUserId !== userId2) {
    throw new Error('Access denied: You can only access conversations you are part of');
  }
  
  // Sort user IDs to ensure consistent conversation ID
  const participants = [userId1, userId2].sort();
  const conversationId = `conv_${participants[0]}_${participants[1]}`;
  
  // Check if conversation already exists
  let conversation = await db.collection('conversations').findOne({ id: conversationId }) as Conversation | null;

  if (!conversation) {
    // Create new conversation
    const newConversation: Conversation = {
      id: conversationId,
      participants,
      lastActivity: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.collection('conversations').insertOne(newConversation);
    conversation = { ...newConversation, _id: result.insertedId };
  }

  return conversation;
}

// Archive a conversation
export async function archiveConversation(conversationId: string, requestingUserId: string) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  // First verify user is part of this conversation
  const conversation = await db.collection('conversations').findOne({ id: conversationId });
  if (!conversation || !conversation.participants.includes(requestingUserId)) {
    throw new Error('Access denied: You are not part of this conversation');
  }
  
  const result = await db.collection('conversations').updateOne(
    { id: conversationId },
    {
      $set: {
        isArchived: true,
        updatedAt: new Date(),
      }
    }
  );
  
  return result;
}
