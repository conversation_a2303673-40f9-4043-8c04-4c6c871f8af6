import { ObjectId } from 'mongodb';
export interface User {
    _id?: ObjectId;
    id: string;
    email: string;
    name: string;
    role: 'client' | 'trainer';
    passwordHash: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface AuthTokenPayload {
    userId: string;
    email: string;
    role: string;
    iat?: number;
    exp?: number;
}
export declare function hashPassword(password: string): Promise<string>;
export declare function verifyPassword(password: string, hash: string): Promise<boolean>;
export declare function generateToken(user: User): string;
export declare function verifyToken(token: string): AuthTokenPayload | null;
export declare function registerUser(email: string, password: string, name: string, role: 'client' | 'trainer'): Promise<{
    user: User;
    token: string;
}>;
export declare function loginUser(email: string, password: string): Promise<{
    user: User;
    token: string;
}>;
export declare function getUserById(userId: string): Promise<User | null>;
//# sourceMappingURL=auth.d.ts.map