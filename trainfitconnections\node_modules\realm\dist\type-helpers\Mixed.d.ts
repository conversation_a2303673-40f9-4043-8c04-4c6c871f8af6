import { binding } from "../binding";
import type { TypeHelpers, TypeOptions } from "./types";
/**
 * Convert an SDK value to a Binding value representation.
 * @param realm The Realm used.
 * @param value The value to convert.
 * @param options Options needed.
 * @param options.isQueryArg Whether the value to convert is a query argument used
 *  for `OrderedCollection.filtered()`. If so, this will be validated differently.
 * @returns The `MixedArg` binding representation.
 * @internal
 */
export declare function mixedToBinding(realm: binding.Realm, value: unknown, { isQueryArg }?: {
    isQueryArg: boolean;
}): binding.MixedArg;
/** @internal */
export declare function createMixedTypeHelpers(options: TypeOptions): TypeHelpers;
