/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/add-client`; params?: Router.UnknownInputParams; } | { pathname: `/add-meal-plan`; params?: Router.UnknownInputParams; } | { pathname: `/add-workout-plan`; params?: Router.UnknownInputParams; } | { pathname: `/billing-history`; params?: Router.UnknownInputParams; } | { pathname: `/book-session`; params?: Router.UnknownInputParams; } | { pathname: `/cash-out`; params?: Router.UnknownInputParams; } | { pathname: `/edit-client`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/error-boundary`; params?: Router.UnknownInputParams; } | { pathname: `/help-support`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/new-session`; params?: Router.UnknownInputParams; } | { pathname: `/revenue`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/subscriptions`; params?: Router.UnknownInputParams; } | { pathname: `/trainer-map`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/server`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/middleware/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/routes/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/sessions-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/workout-plans-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/meal-plans-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/messages-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/media-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/payments-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/notifications-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../lib/api`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/forgot-password` | `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/clients` | `/clients`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/meal-plans` | `/meal-plans`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/my-trainers` | `/my-trainers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/schedule` | `/schedule`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workout-plans` | `/workout-plans`; params?: Router.UnknownInputParams; } | { pathname: `/messages/chat`; params?: Router.UnknownInputParams; } | { pathname: `/messages`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/add-photo`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/live`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/photos`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/videos`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/client/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/edit-meal-plan/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/meal/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reminder/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/session/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trainer/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trainer/edit-video/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trainer/video/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/workout/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/add-client`; params?: Router.UnknownOutputParams; } | { pathname: `/add-meal-plan`; params?: Router.UnknownOutputParams; } | { pathname: `/add-workout-plan`; params?: Router.UnknownOutputParams; } | { pathname: `/billing-history`; params?: Router.UnknownOutputParams; } | { pathname: `/book-session`; params?: Router.UnknownOutputParams; } | { pathname: `/cash-out`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-client`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownOutputParams; } | { pathname: `/error-boundary`; params?: Router.UnknownOutputParams; } | { pathname: `/help-support`; params?: Router.UnknownOutputParams; } | { pathname: `/modal`; params?: Router.UnknownOutputParams; } | { pathname: `/new-session`; params?: Router.UnknownOutputParams; } | { pathname: `/revenue`; params?: Router.UnknownOutputParams; } | { pathname: `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/subscriptions`; params?: Router.UnknownOutputParams; } | { pathname: `/trainer-map`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/server`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/auth`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/middleware/auth`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/routes/auth`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/sessions-crud`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/workout-plans-crud`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/meal-plans-crud`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/messages-crud`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/media-crud`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/payments-crud`; params?: Router.UnknownOutputParams; } | { pathname: `/../backend/lib/notifications-crud`; params?: Router.UnknownOutputParams; } | { pathname: `/../lib/api`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/forgot-password` | `/forgot-password`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/clients` | `/clients`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/meal-plans` | `/meal-plans`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/my-trainers` | `/my-trainers`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/schedule` | `/schedule`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/workout-plans` | `/workout-plans`; params?: Router.UnknownOutputParams; } | { pathname: `/messages/chat`; params?: Router.UnknownOutputParams; } | { pathname: `/messages`; params?: Router.UnknownOutputParams; } | { pathname: `/trainer/add-photo`; params?: Router.UnknownOutputParams; } | { pathname: `/trainer/live`; params?: Router.UnknownOutputParams; } | { pathname: `/trainer/photos`; params?: Router.UnknownOutputParams; } | { pathname: `/trainer/videos`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/client/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/edit-meal-plan/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/meal/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/reminder/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/session/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/trainer/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/trainer/edit-video/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/trainer/video/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/workout/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/add-client${`?${string}` | `#${string}` | ''}` | `/add-meal-plan${`?${string}` | `#${string}` | ''}` | `/add-workout-plan${`?${string}` | `#${string}` | ''}` | `/billing-history${`?${string}` | `#${string}` | ''}` | `/book-session${`?${string}` | `#${string}` | ''}` | `/cash-out${`?${string}` | `#${string}` | ''}` | `/edit-client${`?${string}` | `#${string}` | ''}` | `/edit-profile${`?${string}` | `#${string}` | ''}` | `/error-boundary${`?${string}` | `#${string}` | ''}` | `/help-support${`?${string}` | `#${string}` | ''}` | `/modal${`?${string}` | `#${string}` | ''}` | `/new-session${`?${string}` | `#${string}` | ''}` | `/revenue${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `/subscriptions${`?${string}` | `#${string}` | ''}` | `/trainer-map${`?${string}` | `#${string}` | ''}` | `/../backend/server${`?${string}` | `#${string}` | ''}` | `/../backend/lib/auth${`?${string}` | `#${string}` | ''}` | `/../backend/middleware/auth${`?${string}` | `#${string}` | ''}` | `/../backend/routes/auth${`?${string}` | `#${string}` | ''}` | `/../backend/lib/sessions-crud${`?${string}` | `#${string}` | ''}` | `/../backend/lib/workout-plans-crud${`?${string}` | `#${string}` | ''}` | `/../backend/lib/meal-plans-crud${`?${string}` | `#${string}` | ''}` | `/../backend/lib/messages-crud${`?${string}` | `#${string}` | ''}` | `/../backend/lib/media-crud${`?${string}` | `#${string}` | ''}` | `/../backend/lib/payments-crud${`?${string}` | `#${string}` | ''}` | `/../backend/lib/notifications-crud${`?${string}` | `#${string}` | ''}` | `/../lib/api${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/forgot-password${`?${string}` | `#${string}` | ''}` | `/forgot-password${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/register${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/clients${`?${string}` | `#${string}` | ''}` | `/clients${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/meal-plans${`?${string}` | `#${string}` | ''}` | `/meal-plans${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/my-trainers${`?${string}` | `#${string}` | ''}` | `/my-trainers${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/notifications${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/schedule${`?${string}` | `#${string}` | ''}` | `/schedule${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/workout-plans${`?${string}` | `#${string}` | ''}` | `/workout-plans${`?${string}` | `#${string}` | ''}` | `/messages/chat${`?${string}` | `#${string}` | ''}` | `/messages${`?${string}` | `#${string}` | ''}` | `/trainer/add-photo${`?${string}` | `#${string}` | ''}` | `/trainer/live${`?${string}` | `#${string}` | ''}` | `/trainer/photos${`?${string}` | `#${string}` | ''}` | `/trainer/videos${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/add-client`; params?: Router.UnknownInputParams; } | { pathname: `/add-meal-plan`; params?: Router.UnknownInputParams; } | { pathname: `/add-workout-plan`; params?: Router.UnknownInputParams; } | { pathname: `/billing-history`; params?: Router.UnknownInputParams; } | { pathname: `/book-session`; params?: Router.UnknownInputParams; } | { pathname: `/cash-out`; params?: Router.UnknownInputParams; } | { pathname: `/edit-client`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/error-boundary`; params?: Router.UnknownInputParams; } | { pathname: `/help-support`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/new-session`; params?: Router.UnknownInputParams; } | { pathname: `/revenue`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/subscriptions`; params?: Router.UnknownInputParams; } | { pathname: `/trainer-map`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/server`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/middleware/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/routes/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/sessions-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/workout-plans-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/meal-plans-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/messages-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/media-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/payments-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../backend/lib/notifications-crud`; params?: Router.UnknownInputParams; } | { pathname: `/../lib/api`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/forgot-password` | `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/clients` | `/clients`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/meal-plans` | `/meal-plans`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/my-trainers` | `/my-trainers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/schedule` | `/schedule`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workout-plans` | `/workout-plans`; params?: Router.UnknownInputParams; } | { pathname: `/messages/chat`; params?: Router.UnknownInputParams; } | { pathname: `/messages`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/add-photo`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/live`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/photos`; params?: Router.UnknownInputParams; } | { pathname: `/trainer/videos`; params?: Router.UnknownInputParams; } | `/+not-found` | `/client/${Router.SingleRoutePart<T>}` | `/edit-meal-plan/${Router.SingleRoutePart<T>}` | `/meal/${Router.SingleRoutePart<T>}` | `/reminder/${Router.SingleRoutePart<T>}` | `/session/${Router.SingleRoutePart<T>}` | `/trainer/${Router.SingleRoutePart<T>}` | `/trainer/edit-video/${Router.SingleRoutePart<T>}` | `/trainer/video/${Router.SingleRoutePart<T>}` | `/workout/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/client/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/edit-meal-plan/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/meal/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reminder/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/session/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trainer/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trainer/edit-video/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trainer/video/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/workout/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
