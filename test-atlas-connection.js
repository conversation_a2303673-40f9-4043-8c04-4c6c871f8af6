#!/usr/bin/env node

/**
 * Test Atlas App Services Connection
 * Verifies that the Atlas SDK can connect to your app
 */

const Realm = require('realm');

const ATLAS_APP_ID = 'trainfit-backend-sjcdpns';

async function testAtlasConnection() {
  console.log('🔍 Testing Atlas App Services Connection...');
  console.log('==========================================');
  
  try {
    // Initialize the app
    console.log('📱 Initializing Atlas App...');
    const app = new Realm.App({ id: ATLAS_APP_ID });
    console.log('✅ Atlas App initialized');
    
    // Log in anonymously
    console.log('🔐 Logging in anonymously...');
    const user = await app.logIn(Realm.Credentials.anonymous());
    console.log('✅ Anonymous login successful');
    console.log(`   User ID: ${user.id}`);
    
    // Test database access
    console.log('🗄️  Testing database access...');
    const mongodb = user.mongoClient('mongodb-atlas');
    const db = mongodb.db('trainfit');
    const users = db.collection('users');
    
    // Try to count documents (this tests the connection)
    const count = await users.count();
    console.log('✅ Database connection successful');
    console.log(`   Users collection has ${count} documents`);
    
    // Test a simple query
    console.log('🔍 Testing database query...');
    const testUser = await users.findOne({});
    if (testUser) {
      console.log('✅ Database query successful');
      console.log(`   Found user: ${testUser.email || 'No email'}`);
    } else {
      console.log('✅ Database query successful (no users found yet)');
    }
    
    // Logout
    await user.logOut();
    console.log('✅ Logout successful');
    
    console.log('');
    console.log('🎉 ALL TESTS PASSED!');
    console.log('');
    console.log('✅ Your Atlas App Services setup is working correctly!');
    console.log('✅ Ready for EAS build and TestFlight deployment!');
    console.log('✅ Real users will be able to register and login!');
    
  } catch (error) {
    console.error('❌ Atlas connection test failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Check that your Atlas App Services app exists');
    console.log('2. Verify the App ID: trainfit-backend-sjcdpns');
    console.log('3. Ensure your MongoDB cluster is running');
    console.log('4. Check Atlas App Services dashboard for any issues');
    
    process.exit(1);
  }
}

testAtlasConnection();
