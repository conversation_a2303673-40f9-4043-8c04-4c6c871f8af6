import type { CanonicalObjectSchema, Constructor } from "./schema";
import type { binding } from "./binding";
import { RealmObject } from "./Object";
/** @internal */
export declare class ClassMap {
    private mapping;
    private nameByTableKey;
    private static createNamedConstructor;
    private static createClass;
    private static defineProperties;
    constructor(realm: Realm, realmSchema: readonly binding.ObjectSchema[], canonicalRealmSchema: CanonicalObjectSchema[]);
    get<T>(arg: string | binding.TableKey | RealmObject<T> | Constructor<RealmObject<T>>): Constructor<T>;
    getHelpers<T>(arg: string | binding.TableKey | RealmObject<T> | Constructor<RealmObject<T>>): import("./ClassHelpers").ClassHelpers;
}
